package com.cashop.translate.service.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 云服务提供商路由配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "translate")
public class ProviderRouteConfig {

    /**
     * 同步单张图片翻译-供应商权重配置
     */
    private Map<String, Integer> syncImageSingleProviderWeight = new HashMap<>();

    /**
     * 异步批量图片翻译-供应商权重配置
     */
    private Map<String, Integer> asyncImageBatchProviderWeight = new HashMap<>();

    /**
     * 同步单条文本翻译-供应商权重配置
     */
    private Map<String, Integer> syncTextSingleProviderWeight = new HashMap<>();

    /**
     * 异步批量文本翻译-供应商权重配置
     */
    private Map<String, Integer> asyncTextBatchProviderWeight = new HashMap<>();

    /**
     * 图片翻译配置
     */
    private ImageConfig image = new ImageConfig();

    /**
     * 图片翻译配置类
     */
    public static class ImageConfig {
        /**
         * 批量翻译配置
         */
        private BatchConfig batch = new BatchConfig();

        public BatchConfig getBatch() {
            return batch;
        }

        public void setBatch(BatchConfig batch) {
            this.batch = batch;
        }
    }

    /**
     * 批量翻译配置类
     */
    public static class BatchConfig {
        /**
         * 结果获取配置
         */
        private ResultConfig result = new ResultConfig();

        public ResultConfig getResult() {
            return result;
        }

        public void setResult(ResultConfig result) {
            this.result = result;
        }
    }

    /**
     * 结果获取配置类
     */
    public static class ResultConfig {
        /**
         * 批量翻译结果获取类型：local-job 或 xxl-job
         */
        private String type = "local-job";

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    public Map<String, Integer> getSyncImageSingleProviderWeight() {
        return syncImageSingleProviderWeight;
    }

    public void setSyncImageSingleProviderWeight(Map<String, Integer> syncImageSingleProviderWeight) {
        this.syncImageSingleProviderWeight = syncImageSingleProviderWeight;
    }

    public Map<String, Integer> getAsyncImageBatchProviderWeight() {
        return asyncImageBatchProviderWeight;
    }

    public void setAsyncImageBatchProviderWeight(Map<String, Integer> asyncImageBatchProviderWeight) {
        this.asyncImageBatchProviderWeight = asyncImageBatchProviderWeight;
    }

    public Map<String, Integer> getSyncTextSingleProviderWeight() {
        return syncTextSingleProviderWeight;
    }

    public void setSyncTextSingleProviderWeight(Map<String, Integer> syncTextSingleProviderWeight) {
        this.syncTextSingleProviderWeight = syncTextSingleProviderWeight;
    }

    public Map<String, Integer> getAsyncTextBatchProviderWeight() {
        return asyncTextBatchProviderWeight;
    }

    public void setAsyncTextBatchProviderWeight(Map<String, Integer> asyncTextBatchProviderWeight) {
        this.asyncTextBatchProviderWeight = asyncTextBatchProviderWeight;
    }

    public ImageConfig getImage() {
        return image;
    }

    public void setImage(ImageConfig image) {
        this.image = image;
    }

    /**
     * 获取批量翻译结果获取类型（兼容性方法）
     */
    public String getBatchResultType() {
        return image.getBatch().getResult().getType();
    }

    /**
     * 设置批量翻译结果获取类型（兼容性方法）
     */
    public void setBatchResultType(String batchResultType) {
        image.getBatch().getResult().setType(batchResultType);
    }
}
