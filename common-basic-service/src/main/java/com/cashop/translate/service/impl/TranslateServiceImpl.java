package com.cashop.translate.service.impl;

import com.cashop.translate.common.dto.TranslateImageAsyncResult;
import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.CallbackStatusEnum;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.common.enums.RequestStatusEnum;
import com.cashop.translate.common.enums.RequestTypeEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import com.cashop.translate.service.TranslateService;
import com.cashop.translate.service.route.ProviderRouteService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 翻译服务实现
 * 
 * <AUTHOR>
 */
@Service
public class TranslateServiceImpl implements TranslateService {

    private static final Logger logger = LoggerFactory.getLogger(TranslateServiceImpl.class);

    Gson gson = new Gson();

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    @Autowired
    private ProviderRouteService providerRouteService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request) {
        // 生成或使用请求ID
        if (!StringUtils.hasText(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }

        logger.info("开始处理同步图片翻译请求，requestId: {}", request.getRequestId());

        try {
            // 检查请求是否重复
            if (isDuplicateRequest(request.getRequestId())) {
                logger.warn("重复的翻译请求，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("重复的请求ID");
                return CompletableFuture.completedFuture(response);
            }

            // 选择云服务提供商
            CloudTranslateProvider provider = providerRouteService.selectProvider(RequestTypeEnum.SYNC_IMAGE_SINGLE);
            if (provider == null) {
                logger.error("没有可用的同步翻译提供商，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("没有可用的翻译服务提供商");
                return CompletableFuture.completedFuture(response);
            }

            // 创建请求记录
            TranslateRequestRecord record = createRequestRecord(request, RequestTypeEnum.SYNC_IMAGE_SINGLE, provider.getProviderType());
            translateRequestRecordMapper.insert(record);

            // 调用云服务API
            return provider.translateImageSync(request)
                    .thenApply(response -> {
                        // 更新请求记录
                        updateRequestRecord(record.getId(), response);
                        return response;
                    })
                    .exceptionally(throwable -> {
                        logger.error("同步图片翻译异常，requestId: {}", request.getRequestId(), throwable);
                        TranslateResponse response = new TranslateResponse();
                        response.setRequestId(request.getRequestId());
                        response.setSuccess(false);
                        response.setErrorMessage("翻译服务异常: " + throwable.getMessage());
                        
                        // 更新请求记录为失败状态
                        translateRequestRecordMapper.updateCloudApiStatus(
                                record.getId(), "FAILED", null, "FAILED", null, throwable.getMessage(), null, null);
                        
                        return response;
                    });

        } catch (Exception e) {
            logger.error("同步图片翻译处理异常，requestId: {}", request.getRequestId(), e);
            TranslateResponse response = new TranslateResponse();
            response.setRequestId(request.getRequestId());
            response.setSuccess(false);
            response.setErrorMessage("请求处理异常: " + e.getMessage());
            return CompletableFuture.completedFuture(response);
        }
    }

    @Override
    public CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request) {
        // 生成或使用请求ID
        if (!StringUtils.hasText(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }

        logger.info("开始处理批量图片翻译请求，requestId: {}", request.getRequestId());

        try {
            // 检查请求是否重复
            if (isDuplicateRequest(request.getRequestId())) {
                logger.warn("重复的翻译请求，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("重复的请求ID");
                return CompletableFuture.completedFuture(response);
            }

            // 选择云服务提供商
            CloudTranslateProvider provider = providerRouteService.selectProvider(RequestTypeEnum.ASYNC_IMAGE_BATCH);
            if (provider == null) {
                logger.error("没有可用的批量翻译提供商，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("没有可用的翻译服务提供商");
                return CompletableFuture.completedFuture(response);
            }

            // 创建请求记录
            TranslateRequestRecord record = createRequestRecord(request, RequestTypeEnum.ASYNC_IMAGE_BATCH, provider.getProviderType());
            translateRequestRecordMapper.insert(record);

            // 调用云服务API
            return provider.translateImageBatch(request)
                    .thenApply(response -> {
                        // 更新请求记录
                        updateRequestRecord(record.getId(), response);
                        return response;
                    })
                    .exceptionally(throwable -> {
                        logger.error("批量图片翻译异常，requestId: {}", request.getRequestId(), throwable);
                        TranslateResponse response = new TranslateResponse();
                        response.setRequestId(request.getRequestId());
                        response.setSuccess(false);
                        response.setErrorMessage("翻译服务异常: " + throwable.getMessage());
                        
                        // 更新请求记录为失败状态
                        translateRequestRecordMapper.updateCloudApiStatus(
                                record.getId(), "FAILED", null, "FAILED", null, throwable.getMessage(), null, null);
                        
                        return response;
                    });

        } catch (Exception e) {
            logger.error("批量图片翻译处理异常，requestId: {}", request.getRequestId(), e);
            TranslateResponse response = new TranslateResponse();
            response.setRequestId(request.getRequestId());
            response.setSuccess(false);
            response.setErrorMessage("请求处理异常: " + e.getMessage());
            return CompletableFuture.completedFuture(response);
        }
    }

    @Override
    public TranslateResponse getBatchTranslateResult(String requestId) {
        logger.info("获取批量翻译结果，requestId: {}", requestId);

        try {
            // 添加调试日志
            logger.debug("正在查询数据库，requestId: {}", requestId);
            TranslateRequestRecord record = translateRequestRecordMapper.selectByRequestId(requestId);

            if (record == null) {
                logger.warn("未找到翻译请求记录，requestId: {}", requestId);
                // 添加更多调试信息
                logger.debug("数据库查询返回null，可能的原因：1.requestId不存在 2.数据库连接问题 3.SQL查询条件问题");
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(requestId);
                response.setSuccess(false);
                response.setErrorMessage("未找到翻译请求记录");
                return response;
            }

            logger.info("找到翻译请求记录，recordId: {}, requestStatus: {}, taskId: {}",
                    record.getId(), record.getRequestStatus(), record.getTaskId());

            TranslateResponse response = new TranslateResponse();
            response.setRequestId(requestId);
            response.setRequestStatus(record.getRequestStatus());
            response.setCloudApiStatus(record.getCloudApiStatus());
            response.setCloudApiResponse(record.getCloudApiResponse());
            response.setCloudApiAsyncStatus(record.getCloudApiAsyncStatus());
            response.setCloudApiAsyncResponse(record.getCloudApiAsyncResponse());
            response.setTaskId(record.getTaskId());
            response.setErrorMessage(record.getErrorMessage());
            response.setSuccess(true);
            response.setTranslateImageSyncResultUrl(record.getImageSyncResultUrl());
            if(org.apache.commons.lang3.StringUtils.isNotBlank(record.getImageBatchResults())) {
                response.setTranslateImageAsyncResults(gson.fromJson(
                    record.getImageBatchResults(), 
                    new TypeToken<List<TranslateImageAsyncResult>>() {}.getType()
                ));
            }
            response.setTranslateTextResult(record.getTranslatedText());
            return response;
        } catch (Exception e) {
            logger.error("获取批量翻译结果异常，requestId: {}", requestId, e);
            TranslateResponse response = new TranslateResponse();
            response.setRequestId(requestId);
            response.setSuccess(false);
            response.setErrorMessage("获取结果异常: " + e.getMessage());
            return response;
        }
    }

    @Override
    public CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request) {
        logger.info("开始处理同步文本翻译请求，requestId: {}", request.getRequestId());

        // 生成请求ID（如果未提供）
        if (!StringUtils.hasText(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }

        try {
            // 检查请求是否重复
            if (isDuplicateRequest(request.getRequestId())) {
                logger.warn("重复的文本翻译请求，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("重复的请求ID");
                return CompletableFuture.completedFuture(response);
            }

            // 选择云服务提供商
            CloudTranslateProvider provider = providerRouteService.selectProvider(RequestTypeEnum.SYNC_TEXT_SINGLE);
            if (provider == null) {
                logger.error("没有可用的文本翻译提供商，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("没有可用的翻译服务提供商");
                return CompletableFuture.completedFuture(response);
            }

            // 创建请求记录
            TranslateRequestRecord record = createRequestRecord(request, RequestTypeEnum.SYNC_TEXT_SINGLE, provider.getProviderType());
            translateRequestRecordMapper.insert(record);

            // 调用云服务API
            return provider.translateTextSync(request)
                    .thenApply(response -> {
                        // 更新请求记录
                        updateRequestRecord(record.getId(), response);
                        return response;
                    })
                    .exceptionally(throwable -> {
                        logger.error("同步文本翻译异常，requestId: {}", request.getRequestId(), throwable);
                        TranslateResponse response = new TranslateResponse();
                        response.setRequestId(request.getRequestId());
                        response.setSuccess(false);
                        response.setErrorMessage("翻译服务异常: " + throwable.getMessage());

                        // 更新请求记录为失败状态
                        translateRequestRecordMapper.updateCloudApiStatus(
                                record.getId(), "FAILED", null, "FAILED", null, throwable.getMessage(), null, null);

                        return response;
                    });

        } catch (Exception e) {
            logger.error("同步文本翻译处理异常，requestId: {}", request.getRequestId(), e);
            TranslateResponse response = new TranslateResponse();
            response.setRequestId(request.getRequestId());
            response.setSuccess(false);
            response.setErrorMessage("处理异常: " + e.getMessage());
            return CompletableFuture.completedFuture(response);
        }
    }

    @Override
    public CompletableFuture<TranslateResponse> translateTextBatch(TranslateRequest request) {
        // 生成或使用请求ID
        if (!StringUtils.hasText(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }

        logger.info("开始处理异步批量文本翻译请求，requestId: {}", request.getRequestId());

        try {
            // 检查是否为重复请求
            if (isDuplicateRequest(request.getRequestId())) {
                logger.warn("重复的批量文本翻译请求，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("重复的请求ID");
                return CompletableFuture.completedFuture(response);
            }

            // 选择提供商
            CloudTranslateProvider provider = providerRouteService.selectProvider(RequestTypeEnum.ASYNC_TEXT_BATCH);
            if (provider == null) {
                logger.error("没有可用的翻译提供商，requestId: {}", request.getRequestId());
                TranslateResponse response = new TranslateResponse();
                response.setRequestId(request.getRequestId());
                response.setSuccess(false);
                response.setErrorMessage("没有可用的翻译提供商");
                return CompletableFuture.completedFuture(response);
            }

            logger.info("选择的翻译提供商: {}，requestId: {}", provider.getProviderType(), request.getRequestId());

            // 创建请求记录
            TranslateRequestRecord record = createRequestRecord(request, RequestTypeEnum.ASYNC_TEXT_BATCH, provider.getProviderType());

            // 调用云服务API
            return provider.translateTextBatch(request)
                    .thenApply(response -> {
                        // 更新请求记录
                        updateRequestRecord(record.getId(), response);
                        return response;
                    })
                    .exceptionally(throwable -> {
                        logger.error("异步批量文本翻译异常，requestId: {}", request.getRequestId(), throwable);
                        TranslateResponse response = new TranslateResponse();
                        response.setRequestId(request.getRequestId());
                        response.setSuccess(false);
                        response.setErrorMessage("翻译服务异常: " + throwable.getMessage());

                        // 更新请求记录为失败状态
                        translateRequestRecordMapper.updateCloudApiStatus(
                                record.getId(), "FAILED", null, "FAILED", null, throwable.getMessage(), null, null);

                        return response;
                    });

        } catch (Exception e) {
            logger.error("异步批量文本翻译处理异常，requestId: {}", request.getRequestId(), e);
            TranslateResponse response = new TranslateResponse();
            response.setRequestId(request.getRequestId());
            response.setSuccess(false);
            response.setErrorMessage("处理异常: " + e.getMessage());
            return CompletableFuture.completedFuture(response);
        }
    }

    /**
     * 检查是否为重复请求
     */
    private boolean isDuplicateRequest(String requestId) {
        try {
            TranslateRequestRecord existingRecord = translateRequestRecordMapper.selectByRequestId(requestId);
            return existingRecord != null;
        } catch (Exception e) {
            logger.error("检查重复请求异常，requestId: {}", requestId, e);
            return false;
        }
    }

    /**
     * 创建请求记录
     */
    private TranslateRequestRecord createRequestRecord(TranslateRequest request, RequestTypeEnum requestType, ProviderEnum provider) {
        try {
            TranslateRequestRecord record = new TranslateRequestRecord();
            record.setRequestId(request.getRequestId());
            record.setRequestType(requestType.getCode());
            record.setProvider(provider.getCode());
            record.setSourceLanguage(request.getSourceLanguage());
            record.setTargetLanguage(request.getTargetLanguage());
            
            // 计算图片数量（仅对图片翻译）
            int imageCount = 0;
            if (requestType == RequestTypeEnum.SYNC_IMAGE_SINGLE || requestType == RequestTypeEnum.ASYNC_IMAGE_BATCH) {
                if (!CollectionUtils.isEmpty(request.getImageUrls())) {
                    imageCount = request.getImageUrls().size();
                } else if (!CollectionUtils.isEmpty(request.getImageBase64List())) {
                    imageCount = request.getImageBase64List().size();
                }
            }
            record.setImageCount(imageCount);
            
            record.setRequestParams(objectMapper.writeValueAsString(request));
            record.setRequestStatus(RequestStatusEnum.PENDING.getCode());
            // 初始化云API状态为PENDING，等待调用
            record.setCloudApiStatus(RequestStatusEnum.PENDING.getCode());
            record.setRetryCount(0);
            record.setMaxRetryCount(3);

            // 设置回调相关字段
            record.setCallback(request.getCallback());
            if (StringUtils.hasText(request.getCallback())) {
                record.setCallbackStatus(CallbackStatusEnum.INITIAL.getCode());
                record.setCallbackRetryCount(0);
                record.setCallbackMaxRetryCount(3);
            }

            return record;
        } catch (Exception e) {
            logger.error("创建请求记录异常，requestId: {}", request.getRequestId(), e);
            throw new RuntimeException("创建请求记录失败", e);
        }
    }

    /**
     * 更新请求记录
     */
    private void updateRequestRecord(Long recordId, TranslateResponse response) {
        try {
            translateRequestRecordMapper.updateCloudApiStatus(
                    recordId,
                    response.getCloudApiStatus(),
                    response.getCloudApiResponse(),
                    response.getRequestStatus(),
                    response.getTaskId(),
                    response.getErrorMessage(),
                    response.getTranslateImageSyncResultUrl(),
                    response.getTranslateTextResult()
            );
        } catch (Exception e) {
            logger.error("更新请求记录异常，recordId: {}", recordId, e);
        }
    }
}
