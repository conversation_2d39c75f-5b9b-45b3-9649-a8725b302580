package com.cashop.translate.service.provider;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;

import java.util.concurrent.CompletableFuture;

/**
 * 云服务翻译提供商接口
 * 
 * <AUTHOR>
 */
public interface CloudTranslateProvider {

    /**
     * 获取提供商类型
     */
    ProviderEnum getProviderType();

    /**
     * 同步单张图片翻译
     */
    CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request);

    /**
     * 异步批量图片翻译
     */
    CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request);

    /**
     * 同步文本翻译
     */
    CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request);

    /**
     * 获取批量翻译结果
     */
    CompletableFuture<TranslateResponse> getBatchTranslateResult(String taskId);

    /**
     * 检查提供商是否可用
     */
    boolean isAvailable();
}
