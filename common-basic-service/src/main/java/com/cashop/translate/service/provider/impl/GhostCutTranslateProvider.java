package com.cashop.translate.service.provider.impl;

import com.cashop.translate.client.ghostcut.GhostCutTranslateClient;
import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 鬼手剪辑翻译服务提供商实现
 *
 * <AUTHOR>
 */
@Component
public class GhostCutTranslateProvider implements CloudTranslateProvider {

    private static final Logger logger = LoggerFactory.getLogger(GhostCutTranslateProvider.class);

    @Autowired
    private GhostCutTranslateClient ghostCutTranslateClient;

    @Override
    public ProviderEnum getProviderType() {
        return ProviderEnum.GHOSTCUT;
    }

    @Override
    public CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request) {
        logger.info("鬼手剪辑提供商：开始同步图片翻译，requestId: {}", request.getRequestId());
        return ghostCutTranslateClient.translateImageSync(request);
    }

    @Override
    public CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request) {
        logger.info("鬼手剪辑提供商：开始批量图片翻译，requestId: {}", request.getRequestId());
        // 鬼手剪辑不支持批量翻译，但可以通过提交单个任务来实现异步处理
        return ghostCutTranslateClient.submitTranslateTask(request);
    }

    @Override
    public CompletableFuture<TranslateResponse> getBatchTranslateResult(String taskId) {
        logger.info("鬼手剪辑提供商：获取批量翻译结果，taskId: {}", taskId);
        return ghostCutTranslateClient.getBatchTranslateResult(taskId);
    }

    @Override
    public CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request) {
        logger.info("鬼手剪辑提供商：开始同步文本翻译，requestId: {}", request.getRequestId());
        return ghostCutTranslateClient.translateTextSync(request);
    }

    @Override
    public CompletableFuture<TranslateResponse> translateTextBatch(TranslateRequest request) {
        logger.info("鬼手剪辑提供商：开始异步批量文本翻译，requestId: {}", request.getRequestId());
        return ghostCutTranslateClient.translateTextBatch(request);
    }

    @Override
    public boolean isAvailable() {
        return ghostCutTranslateClient.isAvailable();
    }
}
