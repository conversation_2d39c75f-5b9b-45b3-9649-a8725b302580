package com.cashop.translate.service;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;

import java.util.concurrent.CompletableFuture;

/**
 * 翻译服务接口
 * 
 * <AUTHOR>
 */
public interface TranslateService {

    /**
     * 同步单张图片翻译
     */
    CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request);

    /**
     * 异步批量图片翻译
     */
    CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request);

    /**
     * 获取批量翻译结果
     */
    TranslateResponse getBatchTranslateResult(String requestId);

    /**
     * 同步文本翻译
     */
    CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request);
}
