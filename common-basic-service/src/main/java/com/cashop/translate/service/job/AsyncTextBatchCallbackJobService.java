package com.cashop.translate.service.job;

import com.cashop.translate.common.enums.RequestStatusEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 异步批量文本翻译回调定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class AsyncTextBatchCallbackJobService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncTextBatchCallbackJobService.class);

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    @Autowired
    private RestTemplate restTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理异步批量文本翻译回调任务
     */
    public void processAsyncTextBatchCallbackTasks() {
        logger.info("开始处理异步批量文本翻译回调任务");

        try {
            // 查询需要回调的异步批量文本翻译任务
            List<TranslateRequestRecord> callbackRecords = translateRequestRecordMapper.selectPendingAsyncTextBatchCallbackTasks();
            
            if (CollectionUtils.isEmpty(callbackRecords)) {
                logger.debug("没有需要回调的异步批量文本翻译任务");
                return;
            }

            logger.info("找到 {} 个需要回调的异步批量文本翻译任务", callbackRecords.size());

            for (TranslateRequestRecord record : callbackRecords) {
                try {
                    processAsyncTextBatchCallbackTask(record);
                } catch (Exception e) {
                    logger.error("处理异步批量文本翻译回调任务失败，recordId: {}, requestId: {}", 
                            record.getId(), record.getRequestId(), e);
                    
                    // 更新回调状态为失败，增加重试次数
                    int retryCount = (record.getCallbackRetryCount() == null ? 0 : record.getCallbackRetryCount()) + 1;
                    translateRequestRecordMapper.updateCallbackStatus(
                            record.getId(), 
                            2, // 回调失败
                            "回调异常: " + e.getMessage(), 
                            retryCount
                    );
                }
            }

        } catch (Exception e) {
            logger.error("处理异步批量文本翻译回调任务异常", e);
        }
    }

    /**
     * 处理单个异步批量文本翻译回调任务
     */
    private void processAsyncTextBatchCallbackTask(TranslateRequestRecord record) {
        logger.info("开始处理异步批量文本翻译回调任务，recordId: {}, requestId: {}", 
                record.getId(), record.getRequestId());

        try {
            // 更新回调状态为处理中
            int retryCount = (record.getCallbackRetryCount() == null ? 0 : record.getCallbackRetryCount()) + 1;
            translateRequestRecordMapper.updateCallbackStatus(
                    record.getId(), 
                    3, // 回调处理中
                    null, 
                    retryCount
            );

            // 构建回调数据
            Map<String, Object> callbackData = buildCallbackData(record);
            
            // 执行回调
            String callbackResponse = executeCallback(record.getCallback(), callbackData);

            // 更新回调状态为成功
            translateRequestRecordMapper.updateCallbackStatus(
                    record.getId(), 
                    1, // 回调成功
                    callbackResponse, 
                    retryCount
            );

            logger.info("异步批量文本翻译回调任务处理完成，recordId: {}, requestId: {}", 
                    record.getId(), record.getRequestId());

        } catch (Exception e) {
            logger.error("处理异步批量文本翻译回调任务异常，recordId: {}, requestId: {}", 
                    record.getId(), record.getRequestId(), e);
            throw e;
        }
    }

    /**
     * 构建回调数据
     */
    private Map<String, Object> buildCallbackData(TranslateRequestRecord record) {
        Map<String, Object> callbackData = new HashMap<>();
        
        callbackData.put("requestId", record.getRequestId());
        callbackData.put("requestType", record.getRequestType());
        callbackData.put("requestStatus", record.getRequestStatus());
        callbackData.put("sourceLanguage", record.getSourceLanguage());
        callbackData.put("targetLanguage", record.getTargetLanguage());
        callbackData.put("provider", record.getProvider());
        callbackData.put("taskId", record.getTaskId());
        
        // 添加翻译结果
        if (StringUtils.hasText(record.getTranslatedText())) {
            callbackData.put("translatedText", record.getTranslatedText());
        }
        
        // 添加错误信息（如果有）
        if (StringUtils.hasText(record.getErrorMessage())) {
            callbackData.put("errorMessage", record.getErrorMessage());
        }
        
        // 添加时间戳
        callbackData.put("createdTime", record.getCreatedTime());
        callbackData.put("updatedTime", record.getUpdatedTime());
        
        return callbackData;
    }

    /**
     * 执行回调
     */
    private String executeCallback(String callbackUrl, Map<String, Object> callbackData) {
        try {
            logger.info("执行回调，URL: {}, 数据: {}", callbackUrl, callbackData);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Translate-Service-Callback/1.0");

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(callbackData, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                    callbackUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            String responseBody = response.getBody();
            logger.info("回调执行成功，响应状态: {}, 响应内容: {}", response.getStatusCode(), responseBody);
            
            return responseBody;

        } catch (Exception e) {
            logger.error("执行回调失败，URL: {}", callbackUrl, e);
            throw new RuntimeException("回调执行失败: " + e.getMessage(), e);
        }
    }
}
