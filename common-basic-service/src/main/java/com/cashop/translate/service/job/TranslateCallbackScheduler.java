package com.cashop.translate.service.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 翻译结果回调定时任务调度器
 * 
 * <AUTHOR>
 */
@Component
public class TranslateCallbackScheduler {

    private static final Logger logger = LoggerFactory.getLogger(TranslateCallbackScheduler.class);

    @Autowired
    private TranslateCallbackJobService translateCallbackJobService;

    /**
     * 每5分钟执行一次翻译结果回调任务
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void executeCallbackJob() {
        try {
            logger.debug("开始执行翻译结果回调定时任务");
            translateCallbackJobService.executeCallbackJob();
        } catch (Exception e) {
            logger.error("执行翻译结果回调定时任务失败", e);
        }
    }
}
