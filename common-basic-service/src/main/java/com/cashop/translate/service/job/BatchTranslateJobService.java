package com.cashop.translate.service.job;

import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.common.enums.RequestStatusEnum;
import com.cashop.translate.common.enums.RequestTypeEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import com.google.gson.Gson;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 批量翻译定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class BatchTranslateJobService {

    private static final Logger logger = LoggerFactory.getLogger(BatchTranslateJobService.class);

    Gson gson = new Gson();

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    @Autowired
    private List<CloudTranslateProvider> translateProviders;

    /**
     * 本地任务执行标识，防止重叠执行
     */
    private final AtomicBoolean batchResultJobRunning = new AtomicBoolean(false);
    private final AtomicBoolean cleanTimeoutJobRunning = new AtomicBoolean(false);

    /**
     * 调试特定requestId的记录状态
     */
    public void debugRequestRecord(String requestId) {
        logger.info("调试requestId: {} 的记录状态", requestId);

        try {
            TranslateRequestRecord record = translateRequestRecordMapper.selectByRequestId(requestId);
            if (record == null) {
                logger.warn("未找到requestId: {} 的记录", requestId);
                return;
            }

            logger.info("记录详情 - id: {}, requestId: {}, requestType: {}, provider: {}",
                    record.getId(), record.getRequestId(), record.getRequestType(), record.getProvider());
            logger.info("状态信息 - requestStatus: {}, cloudApiStatus: {}, cloudApiAsyncStatus: {}",
                    record.getRequestStatus(), record.getCloudApiStatus(), record.getCloudApiAsyncStatus());
            logger.info("任务信息 - taskId: {}, retryCount: {}, maxRetryCount: {}",
                    record.getTaskId(), record.getRetryCount(), record.getMaxRetryCount());
            logger.info("时间信息 - createdTime: {}, updatedTime: {}",
                    record.getCreatedTime(), record.getUpdatedTime());

            // 检查是否符合selectPendingAsyncRecords的查询条件
            boolean matchesRequestType = RequestTypeEnum.ASYNC_IMAGE_BATCH.getCode().equals(record.getRequestType());
            boolean matchesCloudApiStatus = "SUCCESS".equals(record.getCloudApiStatus());
            boolean matchesAsyncStatus = record.getCloudApiAsyncStatus() == null || "PROCESSING".equals(record.getCloudApiAsyncStatus());
            boolean matchesRetryCount = record.getRetryCount() != null && record.getRetryCount() < 3;
            boolean hasTaskId = record.getTaskId() != null;

            logger.info("查询条件匹配情况 - requestType: {}, cloudApiStatus: {}, asyncStatus: {}, retryCount: {}, hasTaskId: {}",
                    matchesRequestType, matchesCloudApiStatus, matchesAsyncStatus, matchesRetryCount, hasTaskId);

            if (matchesRequestType && matchesCloudApiStatus && matchesAsyncStatus && matchesRetryCount && hasTaskId) {
                logger.info("该记录符合selectPendingAsyncRecords的查询条件，应该被定时任务处理");
            } else {
                logger.warn("该记录不符合selectPendingAsyncRecords的查询条件，不会被定时任务处理");
            }

        } catch (Exception e) {
            logger.error("调试requestId: {} 时发生异常", requestId, e);
        }
    }

    /**
     * 修复批量翻译记录状态
     */
    public void fixBatchRecord(String requestId) {
        logger.info("开始修复批量翻译记录状态，requestId: {}", requestId);

        try {
            TranslateRequestRecord record = translateRequestRecordMapper.selectByRequestId(requestId);
            if (record == null) {
                logger.warn("未找到requestId: {} 的记录，无法修复", requestId);
                return;
            }

            logger.info("修复前记录状态 - requestType: {}, cloudApiStatus: {}, cloudApiAsyncStatus: {}, retryCount: {}, taskId: {}",
                    record.getRequestType(), record.getCloudApiStatus(), record.getCloudApiAsyncStatus(),
                    record.getRetryCount(), record.getTaskId());

            boolean needUpdate = false;

            // 检查并修复request_type
            if (!RequestTypeEnum.ASYNC_IMAGE_BATCH.getCode().equals(record.getRequestType())) {
                logger.info("修复request_type: {} -> {}", record.getRequestType(), RequestTypeEnum.ASYNC_IMAGE_BATCH.getCode());
                record.setRequestType(RequestTypeEnum.ASYNC_IMAGE_BATCH.getCode());
                needUpdate = true;
            }

            // 检查并修复cloud_api_status
            if (record.getCloudApiStatus() == null || !"SUCCESS".equals(record.getCloudApiStatus())) {
                logger.info("修复cloud_api_status: {} -> SUCCESS", record.getCloudApiStatus());
                record.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                needUpdate = true;
            }

            // 检查并修复cloud_api_async_status
            if (record.getCloudApiAsyncStatus() != null &&
                !"PROCESSING".equals(record.getCloudApiAsyncStatus()) &&
                !"SUCCESS".equals(record.getCloudApiAsyncStatus())) {
                logger.info("修复cloud_api_async_status: {} -> null", record.getCloudApiAsyncStatus());
                record.setCloudApiAsyncStatus(null);
                needUpdate = true;
            }

            // 检查并修复retry_count
            if (record.getRetryCount() == null || record.getRetryCount() >= 3) {
                logger.info("修复retry_count: {} -> 0", record.getRetryCount());
                record.setRetryCount(0);
                needUpdate = true;
            }

            // 检查task_id
            if (record.getTaskId() == null || record.getTaskId().trim().isEmpty()) {
                logger.warn("task_id为空，这可能是问题的根源。建议检查批量翻译提交时是否正确设置了task_id");
            }

            if (needUpdate) {
                int updateCount = translateRequestRecordMapper.updateById(record);
                if (updateCount > 0) {
                    logger.info("记录状态修复成功，requestId: {}", requestId);
                } else {
                    logger.error("记录状态修复失败，updateCount: {}", updateCount);
                }
            } else {
                logger.info("记录状态正常，无需修复");
            }

            // 修复后再次调试
            logger.info("修复后重新调试记录状态");
            debugRequestRecord(requestId);

        } catch (Exception e) {
            logger.error("修复批量翻译记录状态时发生异常，requestId: {}", requestId, e);
        }
    }

    /**
     * 批量翻译结果获取任务
     */
    public void batchTranslateResultJob() {
        // 本地模式防止重叠执行
        if (!batchResultJobRunning.compareAndSet(false, true)) {
            logger.warn("批量翻译结果获取任务正在执行中，跳过本次调度");
            return;
        }

        try {
            logger.info("开始执行批量翻译结果获取任务");

            // 查询需要获取异步结果的记录（包括异步批量翻译和鬼手剪辑的同步翻译）
            List<TranslateRequestRecord> pendingRecords = new ArrayList<>();

            // 1. 查询异步批量翻译记录
            String asyncRequestType = RequestTypeEnum.ASYNC_IMAGE_BATCH.getCode();
            String cloudApiStatus = "SUCCESS";
            Integer maxRetryCount = 3;
            List<String> providerList = new ArrayList<>();
            providerList.add(ProviderEnum.ALIYUN.getCode());

            logger.debug("查询异步批量翻译记录 - requestType: {}, cloudApiStatus: {}, maxRetryCount: {}",
                    asyncRequestType, cloudApiStatus, maxRetryCount);

            List<TranslateRequestRecord> asyncRecords = translateRequestRecordMapper.selectPendingAsyncRecords(
                    asyncRequestType, cloudApiStatus, maxRetryCount, providerList);

            if (!CollectionUtils.isEmpty(asyncRecords)) {
                pendingRecords.addAll(asyncRecords);
                logger.info("找到{}条异步批量翻译记录", asyncRecords.size());
            }

            // 2. 查询鬼手剪辑的同步翻译记录（需要异步查询结果）
            String syncRequestType = RequestTypeEnum.SYNC_IMAGE_SINGLE.getCode();
            providerList = new ArrayList<>();
            providerList.add(ProviderEnum.GHOSTCUT.getCode());
            List<TranslateRequestRecord> ghostcutRecords = translateRequestRecordMapper.selectPendingAsyncRecords(
                    syncRequestType, cloudApiStatus, maxRetryCount, providerList);

            if (!CollectionUtils.isEmpty(ghostcutRecords)) {
                pendingRecords.addAll(ghostcutRecords);
                    logger.info("找到{}条鬼手剪辑同步翻译记录", ghostcutRecords.size());
            }

            if (CollectionUtils.isEmpty(pendingRecords)) {
                logger.info("没有需要获取异步结果的记录");
                return;
            }

            logger.info("找到{}条需要获取异步结果的记录", pendingRecords.size());

            // 打印每条记录的详细信息
            for (TranslateRequestRecord record : pendingRecords) {
                logger.debug("待处理记录 - id: {}, requestId: {}, taskId: {}, cloudApiStatus: {}, cloudApiAsyncStatus: {}, retryCount: {}",
                        record.getId(), record.getRequestId(), record.getTaskId(),
                        record.getCloudApiStatus(), record.getCloudApiAsyncStatus(), record.getRetryCount());
            }

            for (TranslateRequestRecord record : pendingRecords) {
                try {
                    processAsyncResult(record);
                } catch (Exception e) {
                    logger.error("处理异步结果异常，recordId: {}, taskId: {}", 
                            record.getId(), record.getTaskId(), e);
                }
            }

            logger.info("批量翻译结果获取任务执行完成");

        } catch (Exception e) {
            logger.error("批量翻译结果获取任务执行异常", e);
        } finally {
            batchResultJobRunning.set(false);
        }
    }

    /**
     * 清理超时任务
     */
    public void cleanTimeoutTasksJob() {
        // 本地模式防止重叠执行
        if (!cleanTimeoutJobRunning.compareAndSet(false, true)) {
            logger.warn("清理超时任务正在执行中，跳过本次调度");
            return;
        }

        try {
            logger.info("开始执行清理超时任务");

            // 查询超时的任务记录（24小时）
            List<TranslateRequestRecord> timeoutRecords = translateRequestRecordMapper.selectTimeoutRecords(24);

            if (CollectionUtils.isEmpty(timeoutRecords)) {
                logger.info("没有超时的任务记录");
                return;
            }

            logger.info("找到{}条超时的任务记录", timeoutRecords.size());

            for (TranslateRequestRecord record : timeoutRecords) {
                try {
                    // 更新为失败状态
                    translateRequestRecordMapper.updateAsyncStatus(
                            record.getId(),
                            "FAILED",
                            record.getCloudApiResponse(),
                            "FAILED",
                            record.getRetryCount(),
                            "任务超时，自动标记为失败",
                            null
                    );
                    
                    logger.info("超时任务已标记为失败，recordId: {}, requestId: {}", 
                            record.getId(), record.getRequestId());
                            
                } catch (Exception e) {
                    logger.error("处理超时任务异常，recordId: {}", record.getId(), e);
                }
            }

            logger.info("清理超时任务执行完成");

        } catch (Exception e) {
            logger.error("清理超时任务执行异常", e);
        } finally {
            cleanTimeoutJobRunning.set(false);
        }
    }

    /**
     * 处理异步结果
     */
    private void processAsyncResult(TranslateRequestRecord record) {
        if (!StringUtils.hasText(record.getTaskId())) {
            logger.warn("任务ID为空，跳过处理，recordId: {}", record.getId());
            return;
        }

        // 获取对应的云服务提供商
        CloudTranslateProvider provider = getProviderByType(ProviderEnum.fromCode(record.getProvider()));
        if (provider == null) {
            logger.error("未找到云服务提供商: {}, recordId: {}", record.getProvider(), record.getId());
            return;
        }

        try {
            // 调用云服务API获取结果
            provider.getBatchTranslateResult(record.getTaskId())
                    .thenAccept(response -> {
                        updateAsyncResult(record, response);
                    })
                    .exceptionally(throwable -> {
                        logger.error("获取异步结果异常，recordId: {}, taskId: {}", 
                                record.getId(), record.getTaskId(), throwable);
                        
                        // 增加重试次数
                        int newRetryCount = record.getRetryCount() + 1;
                        translateRequestRecordMapper.updateAsyncStatus(
                                record.getId(),
                                "FAILED",
                                null,
                                newRetryCount >= record.getMaxRetryCount() ? "FAILED" : "PROCESSING",
                                newRetryCount,
                                "获取异步结果异常: " + throwable.getMessage(),
                                null
                        );
                        
                        return null;
                    });

        } catch (Exception e) {
            logger.error("处理异步结果异常，recordId: {}, taskId: {}", 
                    record.getId(), record.getTaskId(), e);
            
            // 增加重试次数
            int newRetryCount = record.getRetryCount() + 1;
            translateRequestRecordMapper.updateAsyncStatus(
                    record.getId(),
                    "FAILED",
                    null,
                    newRetryCount >= record.getMaxRetryCount() ? "FAILED" : "PROCESSING",
                    newRetryCount,
                    "处理异步结果异常: " + e.getMessage(),
                    null
            );
        }
    }

    /**
     * 更新异步结果
     */
    private void updateAsyncResult(TranslateRequestRecord record, TranslateResponse response) {
        try {
            String asyncStatus = response.getCloudApiAsyncStatus();
            String requestStatus = response.getRequestStatus();
            String errorMessage = response.getErrorMessage();
            
            // 如果还在处理中，不增加重试次数
            int retryCount = record.getRetryCount();
            if (!"PROCESSING".equals(asyncStatus)) {
                retryCount = record.getRetryCount() + 1;
            }

            translateRequestRecordMapper.updateAsyncStatus(
                    record.getId(),
                    asyncStatus,
                    response.getCloudApiAsyncResponse(),
                    requestStatus,
                    retryCount,
                    errorMessage,
                    null != response.getTranslateImageAsyncResults() ? gson.toJson(response.getTranslateImageAsyncResults()) : null
            );

            logger.info("异步结果更新成功，recordId: {}, taskId: {}, status: {}", 
                    record.getId(), record.getTaskId(), asyncStatus);

        } catch (Exception e) {
            logger.error("更新异步结果异常，recordId: {}, taskId: {}", 
                    record.getId(), record.getTaskId(), e);
        }
    }

    /**
     * 根据提供商类型获取提供商实例
     */
    private CloudTranslateProvider getProviderByType(ProviderEnum providerType) {
        return translateProviders.stream()
                .filter(provider -> provider.getProviderType() == providerType)
                .findFirst()
                .orElse(null);
    }
}
