package com.cashop.translate.service.route;

import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.common.enums.RequestTypeEnum;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import com.cashop.translate.service.config.ProviderRouteConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 云服务提供商路由服务
 * 
 * <AUTHOR>
 */
@Service
public class ProviderRouteService {

    private static final Logger logger = LoggerFactory.getLogger(ProviderRouteService.class);

    @Autowired
    private ProviderRouteConfig routeConfig;

    @Autowired
    private List<CloudTranslateProvider> translateProviders;

    /**
     * 根据权重选择云服务提供商
     */
    public CloudTranslateProvider selectProvider(RequestTypeEnum requestType) {
        Map<String, Integer> weightConfig = getWeightConfig(requestType);
        
        if (CollectionUtils.isEmpty(weightConfig)) {
            logger.warn("未配置{}类型的提供商权重，使用默认阿里云", requestType.getDescription());
            return getProviderByType(ProviderEnum.ALIYUN);
        }

        // 过滤可用的提供商
        List<WeightedProvider> availableProviders = new ArrayList<>();
        int totalWeight = 0;
        
        for (Map.Entry<String, Integer> entry : weightConfig.entrySet()) {
            try {
                ProviderEnum providerEnum = ProviderEnum.fromCode(entry.getKey().toUpperCase());
                CloudTranslateProvider provider = getProviderByType(providerEnum);
                
                if (provider != null && provider.isAvailable() && entry.getValue() > 0) {
                    availableProviders.add(new WeightedProvider(provider, entry.getValue()));
                    totalWeight += entry.getValue();
                }
            } catch (Exception e) {
                logger.warn("提供商{}不可用或配置错误: {}", entry.getKey(), e.getMessage());
            }
        }

        if (availableProviders.isEmpty()) {
            logger.error("没有可用的{}类型提供商，使用默认阿里云", requestType.getDescription());
            return getProviderByType(ProviderEnum.ALIYUN);
        }

        // 按权重随机选择
        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;
        
        for (WeightedProvider weightedProvider : availableProviders) {
            currentWeight += weightedProvider.getWeight();
            if (randomWeight < currentWeight) {
                logger.info("选择{}类型提供商: {}, 权重: {}/{}", 
                        requestType.getDescription(), 
                        weightedProvider.getProvider().getProviderType().getDescription(),
                        weightedProvider.getWeight(), 
                        totalWeight);
                return weightedProvider.getProvider();
            }
        }

        // 兜底返回第一个可用提供商
        CloudTranslateProvider fallbackProvider = availableProviders.get(0).getProvider();
        logger.warn("权重选择异常，使用兜底提供商: {}", fallbackProvider.getProviderType().getDescription());
        return fallbackProvider;
    }

    /**
     * 根据提供商类型获取提供商实例
     */
    private CloudTranslateProvider getProviderByType(ProviderEnum providerType) {
        return translateProviders.stream()
                .filter(provider -> provider.getProviderType() == providerType)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取权重配置
     */
    private Map<String, Integer> getWeightConfig(RequestTypeEnum requestType) {
        switch (requestType) {
            case SYNC_IMAGE_SINGLE:
                return routeConfig.getSyncImageSingleProviderWeight();
            case ASYNC_IMAGE_BATCH:
                return routeConfig.getAsyncImageBatchProviderWeight();
            case SYNC_TEXT_SINGLE:
                return routeConfig.getSyncTextSingleProviderWeight();
            case ASYNC_TEXT_BATCH:
                return routeConfig.getAsyncTextBatchProviderWeight();
            default:
                return Collections.emptyMap();
        }
    }

    /**
     * 权重提供商包装类
     */
    private static class WeightedProvider {
        private final CloudTranslateProvider provider;
        private final int weight;

        public WeightedProvider(CloudTranslateProvider provider, int weight) {
            this.provider = provider;
            this.weight = weight;
        }

        public CloudTranslateProvider getProvider() {
            return provider;
        }

        public int getWeight() {
            return weight;
        }
    }
}
