package com.cashop.translate.service.provider.impl;

import com.cashop.translate.client.yiketu.YiKeTuTranslateClient;
import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 易可图翻译服务提供商实现
 *
 * <AUTHOR>
 */
@Component
public class YiKeTuTranslateProvider implements CloudTranslateProvider {

    private static final Logger logger = LoggerFactory.getLogger(YiKeTuTranslateProvider.class);

    @Autowired
    private YiKeTuTranslateClient yiKeTuTranslateClient;

    @Override
    public ProviderEnum getProviderType() {
        return ProviderEnum.YIKETU;
    }

    @Override
    public CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request) {
        logger.info("易可图提供商：开始同步图片翻译，requestId: {}", request.getRequestId());
        return yiKeTuTranslateClient.translateImageSync(request);
    }

    @Override
    public CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request) {
        logger.info("易可图提供商：开始批量图片翻译，requestId: {}", request.getRequestId());
        return yiKeTuTranslateClient.translateImageBatch(request);
    }

    @Override
    public CompletableFuture<TranslateResponse> getBatchTranslateResult(String taskId) {
        logger.info("易可图提供商：获取批量翻译结果，taskId: {}", taskId);
        return yiKeTuTranslateClient.getBatchTranslateResult(taskId);
    }

    @Override
    public CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request) {
        logger.info("易可图提供商：开始同步文本翻译，requestId: {}", request.getRequestId());
        return yiKeTuTranslateClient.translateTextSync(request);
    }

    @Override
    public boolean isAvailable() {
        return yiKeTuTranslateClient.isAvailable();
    }
}
