package com.cashop.address.service;

import java.util.List;

import com.cashop.address.dao.entity.Address;

/**
 * 地址服务接口
 *
 * <AUTHOR>
 */
public interface AddressService {

    /**
     * 初始化地址数据
     * 从JSON文件中读取数据并导入数据库
     *
     * @return 导入结果
     */
    String initAddressData();

    /**
     * 查询所有国家列表
     *
     * @return 国家列表
     */
    List<Address> getAllCountries();

    /**
     * 根据国家编码查询省级行政区划列表
     *
     * @param countryCode 国家编码
     * @return 省级行政区划列表
     */
    List<Address> getProvincesByCountryCode(String countryCode);

    /**
     * 根据省编码查询市级行政区划列表
     *
     * @param provinceCode 省编码
     * @return 市级行政区划列表
     */
    List<Address> getCitiesByProvinceCode(String provinceCode);

    /**
     * 根据编码查询地址信息
     *
     * @param code 地址编码
     * @return 地址信息
     */
    Address getAddressByCode(String code);

    /**
     * 统计地址数量
     *
     * @param level       地址级别
     * @param countryCode 国家编码
     * @return 地址数量
     */
    long countAddresses(Integer level, String countryCode);
}
