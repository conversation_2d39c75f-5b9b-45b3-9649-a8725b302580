package com.cashop.address.service.impl;

import com.cashop.address.dao.entity.Address;
import com.cashop.address.dao.mapper.AddressMapper;
import com.cashop.address.service.AddressService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 地址服务实现类
 *
 * <AUTHOR>
 */
@Service
public class AddressServiceImpl implements AddressService {

    private static final Logger logger = LoggerFactory.getLogger(AddressServiceImpl.class);

    @Autowired
    private AddressMapper addressMapper;

    @Autowired
    private DatabaseInitService databaseInitService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initAddressData() {
        try {
            logger.info("开始初始化地址数据...");

            // 0. 检查并创建表
            if (!databaseInitService.isTableExists()) {
                logger.info("地址表不存在，开始创建...");
                databaseInitService.createAddressTable();
            } else {
                logger.info("地址表已存在");
            }

            // 1. 导入国家数据
            int countryCount = importCountryData();
            logger.info("导入国家数据完成，共 {} 条", countryCount);

            // 2. 导入省级数据
            int provinceCount = importProvinceData();
            logger.info("导入省级数据完成，共 {} 条", provinceCount);

            // 3. 导入市级数据
            int cityCount = importCityData();
            logger.info("导入市级数据完成，共 {} 条", cityCount);

            String result = String.format("地址数据初始化完成！国家：%d条，省级：%d条，市级：%d条",
                    countryCount, provinceCount, cityCount);
            logger.info(result);
            return result;

        } catch (Exception e) {
            logger.error("初始化地址数据失败", e);
            throw new RuntimeException("初始化地址数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导入国家数据
     */
    private int importCountryData() throws IOException {
        List<Address> addresses = new ArrayList<>();
        String filePath = "location_data_country.json";

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.hasText(line)) {
                    JsonNode node = objectMapper.readTree(line);
                    
                    Address address = new Address();
                    address.setCode(node.get("code").asText());
                    address.setName(node.get("text").asText());
                    address.setNameEn(node.get("text").asText());
                    address.setLevel(1); // 国家级别
                    address.setCountryCode(node.get("code").asText());
                    address.setFlagImg(node.get("flagImg").asText());
                    address.setHasChild(1); // 国家都有子级
                    address.setCreateBy("system");
                    address.setCreateTime(LocalDateTime.now());
                    address.setUpdateBy("system");
                    address.setUpdateTime(LocalDateTime.now());
                    address.setStatus(1);
                    
                    addresses.add(address);
                }
            }
        }

        if (!addresses.isEmpty()) {
            // 分批插入，每批1000条
            int batchSize = 1000;
            for (int i = 0; i < addresses.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, addresses.size());
                List<Address> batch = addresses.subList(i, endIndex);
                addressMapper.batchInsert(batch);
            }
        }

        return addresses.size();
    }

    /**
     * 导入省级数据
     */
    private int importProvinceData() throws IOException {
        List<Address> addresses = new ArrayList<>();
        String filePath = "location_data_province.json";

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.hasText(line)) {
                    JsonNode node = objectMapper.readTree(line);
                    
                    Address address = new Address();
                    address.setCode(node.get("pid").asText());
                    address.setName(node.get("nameEn").asText());
                    address.setNameCn(node.get("nameCn").asText());
                    address.setNameEn(node.get("nameEn").asText());
                    address.setNameLocal(node.get("nameLocal").asText());
                    address.setLevel(2); // 省级别
                    address.setCountryCode(node.get("country_code").asText());
                    address.setHasChild(1); // 省都有子级
                    address.setCreateBy("system");
                    address.setCreateTime(LocalDateTime.now());
                    address.setUpdateBy("system");
                    address.setUpdateTime(LocalDateTime.now());
                    address.setStatus(1);
                    
                    addresses.add(address);
                }
            }
        }

        if (!addresses.isEmpty()) {
            // 分批插入，每批1000条
            int batchSize = 1000;
            for (int i = 0; i < addresses.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, addresses.size());
                List<Address> batch = addresses.subList(i, endIndex);
                addressMapper.batchInsert(batch);
            }
        }

        return addresses.size();
    }

    /**
     * 导入市级数据
     */
    private int importCityData() throws IOException {
        List<Address> addresses = new ArrayList<>();
        String filePath = "location_data_city.json";

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.hasText(line)) {
                    JsonNode node = objectMapper.readTree(line);
                    
                    Address address = new Address();
                    address.setCode(node.get("id").asText());
                    address.setName(node.get("nameEn").asText());
                    address.setNameCn(node.get("nameCn").asText());
                    address.setNameEn(node.get("nameEn").asText());
                    address.setNameLocal(node.get("nameLocal").asText());
                    address.setLevel(3); // 市级别
                    address.setParentCode(node.get("state_pid").asText());
                    address.setCountryCode(node.get("country_code").asText());
                    address.setHasChild(0); // 市级没有子级
                    address.setCreateBy("system");
                    address.setCreateTime(LocalDateTime.now());
                    address.setUpdateBy("system");
                    address.setUpdateTime(LocalDateTime.now());
                    address.setStatus(1);
                    
                    addresses.add(address);
                }
            }
        }

        if (!addresses.isEmpty()) {
            // 分批插入，每批1000条
            int batchSize = 1000;
            for (int i = 0; i < addresses.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, addresses.size());
                List<Address> batch = addresses.subList(i, endIndex);
                addressMapper.batchInsert(batch);
            }
        }

        return addresses.size();
    }

    @Override
    public List<Address> getAllCountries() {
        return addressMapper.selectAllCountries();
    }

    @Override
    public List<Address> getProvincesByCountryCode(String countryCode) {
        return addressMapper.selectProvincesByCountryCode(countryCode);
    }

    @Override
    public List<Address> getCitiesByProvinceCode(String provinceCode) {
        return addressMapper.selectCitiesByProvinceCode(provinceCode);
    }

    @Override
    public Address getAddressByCode(String code) {
        return addressMapper.selectByCode(code);
    }

    @Override
    public long countAddresses(Integer level, String countryCode) {
        return addressMapper.countByLevelAndCountryCode(level, countryCode);
    }
}
