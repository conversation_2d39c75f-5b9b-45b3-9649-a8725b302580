package com.cashop.message.service;

import java.util.concurrent.CompletableFuture;

import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.facade.dto.EmailSendResponse;
import com.cashop.message.facade.dto.QueryEmailTemplateRequest;
import com.cashop.message.dao.entity.MessageTemplate;

/**
 * 消息发送服务接口
 *
 * <AUTHOR>
 */
public interface MessageService {

    /**
     * 发送邮件
     *
     * @param request 邮件发送请求
     * @return 邮件发送响应
     */
    CompletableFuture<EmailSendResponse> sendEmail(EmailSendRequest request);

    /**
     * 重试失败的邮件发送
     *
     * @param requestId 请求ID
     * @return 邮件发送响应
     */
    CompletableFuture<EmailSendResponse> retryEmailSend(String requestId);

    /**
     * 查询邮件发送状态
     *
     * @param requestId 请求ID
     * @return 邮件发送响应
     */
    EmailSendResponse getEmailSendStatus(String requestId);

    /**
     * 查询邮件模版
     *
     * @param request 邮件模版查询请求
     * @return 邮件模版查询响应
     */
    MessageTemplate queryEmailTemplate(QueryEmailTemplateRequest request);

    
}
