package com.cashop.message.service.impl;

import com.cashop.common.base.exception.BusinessException;
import com.cashop.message.dao.entity.MessageEmailRequest;
import com.cashop.message.dao.entity.MessageTemplate;
import com.cashop.message.dao.entity.MessageProvider;
import com.cashop.message.dao.mapper.MessageEmailRequestMapper;
import com.cashop.message.dao.mapper.MessageTemplateMapper;
import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.facade.dto.EmailSendResponse;
import com.cashop.message.facade.dto.MessageResponse;
import com.cashop.message.facade.dto.QueryEmailTemplateRequest;
import com.cashop.message.dao.mapper.MessageProviderMapper;
import com.cashop.message.service.MessageService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 消息发送服务实现
 * 
 * <AUTHOR>
 */
@Service
public class MessageServiceImpl implements MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

    @Autowired
    private MessageEmailRequestMapper messageEmailRequestMapper;

    @Autowired
    private MessageTemplateMapper messageTemplateMapper;

    @Autowired
    private MessageProviderMapper messageProviderMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private Configuration freemarkerConfiguration = new Configuration(Configuration.VERSION_2_3_0);

    {
        
        StringTemplateLoader stringLoader = new StringTemplateLoader();
        freemarkerConfiguration.setTemplateLoader(stringLoader);
        freemarkerConfiguration.setNumberFormat("#");
        freemarkerConfiguration.setDefaultEncoding("UTF-8");
    }


    @Override
    public CompletableFuture<EmailSendResponse> sendEmail(EmailSendRequest request) {
        long startTime = System.currentTimeMillis();
        
        // 生成请求ID（如果未提供）
        if (!StringUtils.hasText(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }

        logger.info("开始处理邮件发送请求，requestId: {}, appId: {}, sceneCode: {}", 
                   request.getRequestId(), request.getAppId(), request.getSceneCode());

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 保存邮件发送记录
                MessageEmailRequest emailRequest = createEmailRequest(request);
                messageEmailRequestMapper.insert(emailRequest);

                // 2. 获取消息模版
                MessageTemplate template = getMessageTemplate(request.getSceneCode(), 
                                                             request.getLanguage(), "email");
                if (template == null) {
                    return buildErrorResponse(request.getRequestId(), "未找到对应的邮件模版", startTime);
                }

                // 3. 处理模版参数，生成实际发送内容
                String actualTitle = processTemplate(request.getSceneCode() + "_title", template.getMessageTitle(), request.getTemplateParams());
                String actualContent = processTemplate(request.getSceneCode() + "_content", template.getMessageContent(), request.getTemplateParams());

                // 如果请求中指定了标题，则使用请求中的标题
                if (StringUtils.hasText(request.getEmailTitle())) {
                    actualTitle = request.getEmailTitle();
                }

                // 更新实际发送内容
                messageEmailRequestMapper.updateActualContent(emailRequest.getId(), actualTitle, actualContent);

                // 4. 获取邮件配置
                String host = request.getEmailHost();
                Integer port = request.getEmailPort();
                String fromEmail = request.getFromEmail();
                String fromEmailPwd = request.getFromEmailPwd();

                // 如果请求中没有提供邮件配置，则从供应商配置中获取
                if (!StringUtils.hasText(host) || port == null || 
                    !StringUtils.hasText(fromEmail) || !StringUtils.hasText(fromEmailPwd)) {
                    
                    MessageProvider provider = messageProviderMapper.selectByTypeAndKey("email", template.getProviderKey());
                    if (provider != null) {
                        try {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> config = objectMapper.readValue(provider.getProviderConfig(), Map.class);
                            host = host != null ? host : (String) config.get("host");
                            port = port != null ? port : (Integer) config.get("port");
                            fromEmail = StringUtils.hasText(fromEmail) ? fromEmail : provider.getProviderUserName();
                            fromEmailPwd = StringUtils.hasText(fromEmailPwd) ? fromEmailPwd : provider.getProviderUserPwd();
                        } catch (JsonProcessingException e) {
                            logger.error("解析供应商配置失败", e);
                        }
                    }
                }

                // 5. 发送邮件
                messageEmailRequestMapper.updateSendStatus(emailRequest.getId(), "PROCESSING", null, 0);
                
                MessageResponse mailResponse = MailMessageServiceImpl.sendEmail(
                    host, String.valueOf(port), fromEmail, fromEmailPwd, 
                    request.getToEmailList(), actualTitle, actualContent);

                // 6. 更新发送状态
                if (mailResponse.getSuccess()) {
                    messageEmailRequestMapper.updateSendStatus(emailRequest.getId(), "SUCCESS", null, 0);
                    return buildSuccessResponse(request.getRequestId(), actualTitle, "邮件发送成功", startTime);
                } else {
                    messageEmailRequestMapper.updateSendStatus(emailRequest.getId(), "FAILED", 
                                                             mailResponse.getMessage(), 1);
                    return buildErrorResponse(request.getRequestId(), "邮件发送失败: " + mailResponse.getMessage(), startTime);
                }

            } catch (Exception e) {
                logger.error("邮件发送异常，requestId: {}", request.getRequestId(), e);
                return buildErrorResponse(request.getRequestId(), "邮件发送异常: " + e.getMessage(), startTime);
            }
        });
    }

    @Override
    public CompletableFuture<EmailSendResponse> retryEmailSend(String requestId) {
        logger.info("开始重试邮件发送，requestId: {}", requestId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                MessageEmailRequest emailRequest = messageEmailRequestMapper.selectByRequestId(requestId);
                if (emailRequest == null) {
                    return buildErrorResponse(requestId, "未找到对应的邮件发送记录", System.currentTimeMillis());
                }

                if (emailRequest.getCurrentRetryCount() >= emailRequest.getMaxRetryCount()) {
                    return buildErrorResponse(requestId, "已达到最大重试次数", System.currentTimeMillis());
                }

                // 重新发送邮件
                MessageResponse mailResponse = MailMessageServiceImpl.sendEmail(
                    emailRequest.getEmailHost(), String.valueOf(emailRequest.getEmailPort()),
                    emailRequest.getFromEmail(), emailRequest.getFromEmailPwd(),
                    parseEmailList(emailRequest.getToEmailList()),
                    emailRequest.getActualTitle(), emailRequest.getActualContent());

                // 更新重试状态
                int newRetryCount = emailRequest.getCurrentRetryCount() + 1;
                if (mailResponse.getSuccess()) {
                    messageEmailRequestMapper.updateSendStatus(emailRequest.getId(), "SUCCESS", null, newRetryCount);
                    return buildSuccessResponse(requestId, emailRequest.getActualTitle(), "邮件重试发送成功", System.currentTimeMillis());
                } else {
                    messageEmailRequestMapper.updateSendStatus(emailRequest.getId(), "FAILED", 
                                                             mailResponse.getMessage(), newRetryCount);
                    return buildErrorResponse(requestId, "邮件重试发送失败: " + mailResponse.getMessage(), System.currentTimeMillis());
                }

            } catch (Exception e) {
                logger.error("邮件重试发送异常，requestId: {}", requestId, e);
                return buildErrorResponse(requestId, "邮件重试发送异常: " + e.getMessage(), System.currentTimeMillis());
            }
        });
    }

    @Override
    public EmailSendResponse getEmailSendStatus(String requestId) {
        try {
            MessageEmailRequest emailRequest = messageEmailRequestMapper.selectByRequestId(requestId);
            if (emailRequest == null) {
                return buildErrorResponse(requestId, "未找到对应的邮件发送记录", System.currentTimeMillis());
            }

            EmailSendResponse response = new EmailSendResponse();
            response.setRequestId(requestId);
            response.setSuccess("SUCCESS".equals(emailRequest.getSendStatus()));
            response.setSendStatus(emailRequest.getSendStatus());
            response.setActualTitle(emailRequest.getActualTitle());
            response.setErrorMessage(emailRequest.getErrorMessage());
            response.setMessage("查询成功");

            return response;

        } catch (Exception e) {
            logger.error("查询邮件发送状态异常，requestId: {}", requestId, e);
            return buildErrorResponse(requestId, "查询异常: " + e.getMessage(), System.currentTimeMillis());
        }
    }

    /**
     * 创建邮件发送记录
     */
    private MessageEmailRequest createEmailRequest(EmailSendRequest request) {
        MessageEmailRequest emailRequest = new MessageEmailRequest();
        emailRequest.setRequestId(request.getRequestId());
        emailRequest.setAppId(request.getAppId());
        emailRequest.setSceneCode(request.getSceneCode());
        emailRequest.setLanguage(request.getLanguage());
        emailRequest.setEmailHost(request.getEmailHost());
        emailRequest.setEmailPort(request.getEmailPort());
        emailRequest.setFromEmail(request.getFromEmail());
        emailRequest.setFromEmailPwd(request.getFromEmailPwd());
        
        try {
            emailRequest.setToEmailList(objectMapper.writeValueAsString(request.getToEmailList()));
            if (request.getCcEmailList() != null) {
                emailRequest.setCcEmailList(objectMapper.writeValueAsString(request.getCcEmailList()));
            }
            if (request.getAttachmentList() != null) {
                emailRequest.setAttachmentList(objectMapper.writeValueAsString(request.getAttachmentList()));
            }
            if (request.getTemplateParams() != null) {
                emailRequest.setTemplateParams(objectMapper.writeValueAsString(request.getTemplateParams()));
            }
        } catch (JsonProcessingException e) {
            logger.error("序列化请求参数失败", e);
        }
        
        emailRequest.setRequestTitle(request.getEmailTitle());
        emailRequest.setCurrentRetryCount(0);
        emailRequest.setMaxRetryCount(3);
        emailRequest.setSendStatus("PENDING");
        
        return emailRequest;
    }

    /**
     * 获取消息模版
     */
    private MessageTemplate getMessageTemplate(String sceneCode, String language, String messageType) {
        MessageTemplate template = messageTemplateMapper.selectBySceneAndLanguageAndType(sceneCode, language, messageType);
        if (template == null && !"en".equals(language)) {
            // 如果没找到对应语言的模版，尝试使用英文模版
            template = messageTemplateMapper.selectBySceneAndLanguageAndType(sceneCode, "en", messageType);
        }
        return template;
    }

    /**
     * 处理模版参数
     */
    private String processTemplate(String id, String template, Map<String, Object> variables) {
        if (template == null || variables == null) {
            return template;
        }
        
        StringWriter stringWriter = new StringWriter();
        try {
            freemarker.template.Template freemarkerTemplate = new freemarker.template.Template(id, template, freemarkerConfiguration);
            freemarkerTemplate.process(variables, stringWriter);
        } catch (Exception e) {
            throw new BusinessException("处理模版参数失败", e);
        }
        
        String result = stringWriter.toString();
        /*
        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replaceAll("\\$\\{" + entry.getKey() + "\\}", value);
        }
        */
        return result;
    }

    /**
     * 解析邮件列表
     */
    @SuppressWarnings("unchecked")
    private List<String> parseEmailList(String emailListJson) {
        try {
            return objectMapper.readValue(emailListJson, List.class);
        } catch (JsonProcessingException e) {
            logger.error("解析邮件列表失败", e);
            return null;
        }
    }

    /**
     * 构建成功响应
     */
    private EmailSendResponse buildSuccessResponse(String requestId, String actualTitle, String message, long startTime) {
        EmailSendResponse response = new EmailSendResponse();
        response.setRequestId(requestId);
        response.setSuccess(true);
        response.setMessage(message);
        response.setSendStatus("SUCCESS");
        response.setActualTitle(actualTitle);
        response.setProcessingTime(System.currentTimeMillis() - startTime);
        return response;
    }

    /**
     * 构建错误响应
     */
    private EmailSendResponse buildErrorResponse(String requestId, String errorMessage, long startTime) {
        EmailSendResponse response = new EmailSendResponse();
        response.setRequestId(requestId);
        response.setSuccess(false);
        response.setMessage("邮件发送失败");
        response.setSendStatus("FAILED");
        response.setErrorMessage(errorMessage);
        response.setProcessingTime(System.currentTimeMillis() - startTime);
        return response;
    }

    @Override
    public MessageTemplate queryEmailTemplate(QueryEmailTemplateRequest request) {
        return messageTemplateMapper.selectBySceneAndLanguageAndType(request.getSceneCode(), request.getLanguage(), "email");
    }

}
