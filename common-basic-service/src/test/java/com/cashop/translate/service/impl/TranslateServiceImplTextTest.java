package com.cashop.translate.service.impl;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.ProviderEnum;
import com.cashop.translate.common.enums.RequestTypeEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.cashop.translate.service.provider.CloudTranslateProvider;
import com.cashop.translate.service.route.ProviderRouteService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TranslateServiceImpl 文本翻译功能单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class TranslateServiceImplTextTest {

    @Mock
    private ProviderRouteService providerRouteService;

    @Mock
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    @Mock
    private CloudTranslateProvider mockProvider;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private TranslateServiceImpl translateService;

    private TranslateRequest textRequest;
    private TranslateResponse successResponse;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        textRequest = new TranslateRequest();
        textRequest.setRequestId("test-text-001");
        textRequest.setText("Hello, world!");
        textRequest.setSourceLanguage("en");
        textRequest.setTargetLanguage("zh");

        successResponse = new TranslateResponse();
        successResponse.setRequestId("test-text-001");
        successResponse.setSuccess(true);
        successResponse.setCloudApiStatus("SUCCESS");
        successResponse.setRequestStatus("SUCCESS");
        successResponse.setCloudApiResponse("{\"translated\":\"你好，世界！\"}");
    }

    @Test
    void testTranslateTextSync_Success() throws Exception {
        // Mock 依赖
        when(translateRequestRecordMapper.selectByRequestId(anyString())).thenReturn(null);
        when(providerRouteService.selectProvider(RequestTypeEnum.SYNC_TEXT_SINGLE)).thenReturn(mockProvider);
        when(mockProvider.getProviderType()).thenReturn(ProviderEnum.ALIYUN);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(mockProvider.translateTextSync(any(TranslateRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

        // 执行测试
        CompletableFuture<TranslateResponse> result = translateService.translateTextSync(textRequest);
        TranslateResponse response = result.get();

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("test-text-001", response.getRequestId());
        assertEquals("SUCCESS", response.getCloudApiStatus());

        // 验证方法调用
        verify(translateRequestRecordMapper).selectByRequestId("test-text-001");
        verify(providerRouteService).selectProvider(RequestTypeEnum.SYNC_TEXT_SINGLE);
        verify(mockProvider).translateTextSync(textRequest);
        verify(translateRequestRecordMapper).insert(any(TranslateRequestRecord.class));
        verify(translateRequestRecordMapper).updateCloudApiStatus(anyLong(), eq("SUCCESS"), anyString(), eq("SUCCESS"), isNull(), isNull(), null, null);
    }

    @Test
    void testTranslateTextSync_DuplicateRequest() throws Exception {
        // Mock 重复请求
        TranslateRequestRecord existingRecord = new TranslateRequestRecord();
        existingRecord.setRequestId("test-text-001");
        when(translateRequestRecordMapper.selectByRequestId("test-text-001")).thenReturn(existingRecord);

        // 执行测试
        CompletableFuture<TranslateResponse> result = translateService.translateTextSync(textRequest);
        TranslateResponse response = result.get();

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("test-text-001", response.getRequestId());
        assertEquals("重复的请求ID", response.getErrorMessage());

        // 验证不会调用翻译服务
        verify(providerRouteService, never()).selectProvider(any());
        verify(mockProvider, never()).translateTextSync(any());
    }

    @Test
    void testTranslateTextSync_NoAvailableProvider() throws Exception {
        // Mock 没有可用提供商
        when(translateRequestRecordMapper.selectByRequestId(anyString())).thenReturn(null);
        when(providerRouteService.selectProvider(RequestTypeEnum.SYNC_TEXT_SINGLE)).thenReturn(null);

        // 执行测试
        CompletableFuture<TranslateResponse> result = translateService.translateTextSync(textRequest);
        TranslateResponse response = result.get();

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("test-text-001", response.getRequestId());
        assertEquals("没有可用的翻译服务提供商", response.getErrorMessage());

        // 验证不会调用翻译服务
        verify(mockProvider, never()).translateTextSync(any());
    }

    @Test
    void testTranslateTextSync_ProviderException() throws Exception {
        // Mock 提供商异常
        when(translateRequestRecordMapper.selectByRequestId(anyString())).thenReturn(null);
        when(providerRouteService.selectProvider(RequestTypeEnum.SYNC_TEXT_SINGLE)).thenReturn(mockProvider);
        when(mockProvider.getProviderType()).thenReturn(ProviderEnum.ALIYUN);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        
        CompletableFuture<TranslateResponse> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Provider error"));
        when(mockProvider.translateTextSync(any(TranslateRequest.class))).thenReturn(failedFuture);

        // 执行测试
        CompletableFuture<TranslateResponse> result = translateService.translateTextSync(textRequest);
        TranslateResponse response = result.get();

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("test-text-001", response.getRequestId());
        assertTrue(response.getErrorMessage().contains("翻译服务异常"));

        // 验证错误状态更新
        verify(translateRequestRecordMapper).updateCloudApiStatus(anyLong(), eq("FAILED"), isNull(), eq("FAILED"), isNull(), anyString(), null, null);
    }

    @Test
    void testTranslateTextSync_AutoGenerateRequestId() throws Exception {
        // 准备没有requestId的请求
        TranslateRequest requestWithoutId = new TranslateRequest();
        requestWithoutId.setText("Hello, world!");
        requestWithoutId.setSourceLanguage("en");
        requestWithoutId.setTargetLanguage("zh");

        // Mock 依赖
        when(translateRequestRecordMapper.selectByRequestId(anyString())).thenReturn(null);
        when(providerRouteService.selectProvider(RequestTypeEnum.SYNC_TEXT_SINGLE)).thenReturn(mockProvider);
        when(mockProvider.getProviderType()).thenReturn(ProviderEnum.ALIYUN);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(mockProvider.translateTextSync(any(TranslateRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(successResponse));

        // 执行测试
        CompletableFuture<TranslateResponse> result = translateService.translateTextSync(requestWithoutId);
        TranslateResponse response = result.get();

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(requestWithoutId.getRequestId()); // 应该自动生成了requestId

        // 验证方法调用
        verify(mockProvider).translateTextSync(requestWithoutId);
    }
}
