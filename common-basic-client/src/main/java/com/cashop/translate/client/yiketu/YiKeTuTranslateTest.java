package com.cashop.translate.client.yiketu;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;

import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSON;
import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/*

易可图-图片翻译 只支持单张翻译，同步返回翻译结果

请求地址
环境	https地址
易可图	https://open-api.yiketu.com
Digshow(跨境)	https://open-api.digshow.com

*/
public class YiKeTuTranslateTest {

    private static final Logger logger = LoggerFactory.getLogger(YiKeTuTranslateTest.class);

    public static void main(String[] args) {
        
        
        String appKey = "4909865810";
        String appSecret = "db8870ca84e32b7ea0bf10a61d2eaa01";

        YiKeTuTranslateTest yiKeTuTranslateTest = new YiKeTuTranslateTest();

        /* 
        Map<String, Object> params = new TreeMap<>();
        params.put("timestamp", System.currentTimeMillis()/1000);
        params.put("appKey", "4909865810");
        params.put("pageNo", "1");
        params.put("pageSize", "10");
        System.out.println(yiKeTuTranslateTest.sign(params, "db8870ca84e32b7ea0bf10a61d2eaa01"));
        */
        /*
        System.out.println(yiKeTuTranslateTest.getTranslateImg(
            appKey, 
            appSecret,
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/10.jpg"));
        //https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/4.JPG
        //{"result":"success","code":200,"data":{"translateImgUrl":"https:\/\/ykt-rt.yiketu.com\/tmp\/days\/360\/user_translate_img\/result_img\/0\/20250829\/793ec3ff4d4b1cd50a9accf3ba61243d.jpg","imageEditor":"{\"id\":\"127f1ba6-84c2-11f0-afc5-00163e198bc2\",\"language\":\"en\",\"type\":\"template\",\"layers\":[{\"type\":\"image\",\"top\":400,\"left\":400,\"width\":800,\"height\":800,\"scaleX\":1,\"scaleY\":1,\"selectable\":false,\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"d48c05af-f7fc-48c3-91ac-4b4772feea00\",\"src\":\"https:\/\/aib-image.oss-ap-southeast-1.aliyuncs.com\/tufan%2F122a64bc-84c2-11f0-a091-00163e198bc2.png?OSSAccessKeyId=LTAI5tSEGjGp5wixZgHLc3bV&Expires=4973134951&Signature=9MFzcSHgGAoeSg0n68x%2FtPFS%2BbM%3D\",\"zIndex\":1},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"42ba78d8-9651-481c-8b8f-1d992be6c513\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":729,\"left\":97,\"width\":124,\"height\":126,\"fontSize\":58,\"content\":\"Buy now\",\"originalFontSize\":58,\"originalContent\":\"\u7acb\u5373 \u62a2\u8d2d\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"right\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#fdfdfd\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":2,\"refId\":\"f4f9e163-236a-4c43-99e3-fdd93754bfa8\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"f4f9e163-236a-4c43-99e3-fdd93754bfa8\",\"type\":\"image\",\"top\":722.5,\"left\":96.5,\"width\":151,\"height\":153,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/c2e67419-5890-4438-901f-1dc9eb38f5ba.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=D1TjFUExZ4AdsqauTi96J9qnM%2B4%3D\",\"zIndex\":2,\"refId\":\"42ba78d8-9651-481c-8b8f-1d992be6c513\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"01063a4d-c2f6-47c9-a52e-0647dd7bf89b\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":765,\"left\":507,\"width\":560,\"height\":36,\"fontSize\":21,\"content\":\"3.6g\/100ml milk protein, original and good nutrition\",\"originalFontSize\":21,\"originalContent\":\"3.6g\/100mL\u4e73\u86cb\u767d \u539f\u751f\u597d\u8425\u517b\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#323024\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":5,\"refId\":\"d4d571db-86cf-4200-a88c-d472d4002bf8\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"d4d571db-86cf-4200-a88c-d472d4002bf8\",\"type\":\"image\",\"top\":765,\"left\":506.5,\"width\":575,\"height\":60,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/43a40500-a8f1-472a-a4e8-41bbd5b7806a.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=kbXI7WJ6dzowUU9lZHoHD5jFFyQ%3D\",\"zIndex\":5,\"refId\":\"01063a4d-c2f6-47c9-a52e-0647dd7bf89b\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"a9c1ed96-b536-4389-a96b-9f16be41b4f9\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":697,\"left\":473,\"width\":486,\"height\":24,\"fontSize\":23,\"content\":\"Royal beef pure milk 160ml*12 bags*1 box\",\"originalFontSize\":23,\"originalContent\":\"\u7687\u6c0f\u6c34\u725b\u7eaf\u725b\u5976160mL*12\u888b*1\u7bb1\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#2f3124\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":4,\"refId\":\"83932442-0b25-49b4-abc5-827b51a9d7c9\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"83932442-0b25-49b4-abc5-827b51a9d7c9\",\"type\":\"image\",\"top\":696.5,\"left\":473,\"width\":414,\"height\":39,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/d0c4007f-cd10-4ad2-b541-94806e4fc56e.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=Tz6w24YPBbzzcEUBdieG0j4uQVc%3D\",\"zIndex\":4,\"refId\":\"a9c1ed96-b536-4389-a96b-9f16be41b4f9\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"98dab7e5-329b-4bcd-af93-d6df0c7cd7c5\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":161,\"left\":403,\"width\":550,\"height\":52,\"fontSize\":25,\"content\":\"Portable bag with delicious taste at any time\",\"originalFontSize\":25,\"originalContent\":\"\u4fbf\u643a\u888b\u88c5 \u7f8e\u5473\u968f\u65f6\u4eab\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#000000\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":3,\"refId\":\"476da98c-3261-4c82-91de-9a97931a1e61\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"476da98c-3261-4c82-91de-9a97931a1e61\",\"type\":\"image\",\"top\":161,\"left\":403.5,\"width\":573,\"height\":84,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/e776f78a-490a-4198-9066-b0bc1f65cbf2.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=8iPk9YhiYEnMbqhiGe2vXBsqKdw%3D\",\"zIndex\":3,\"refId\":\"98dab7e5-329b-4bcd-af93-d6df0c7cd7c5\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"d09758d0-a546-4640-baa6-c1ade32bf891\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":601,\"left\":235,\"width\":316,\"height\":14,\"fontSize\":13,\"content\":\"Net content: 160ml\",\"originalFontSize\":13,\"originalContent\":\"\u51c0\u542b\u91cf\uff1a160mL\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#50514c\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":7,\"refId\":\"860fed32-0d18-4910-bb7a-e60fc2d84595\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"860fed32-0d18-4910-bb7a-e60fc2d84595\",\"type\":\"image\",\"top\":600.5,\"left\":235,\"width\":68,\"height\":23,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/1d3ffa6a-f86b-4a9c-8ff0-e8ce252766de.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=ru8hFlPcZvwuEOftXe247XQS008%3D\",\"zIndex\":7,\"refId\":\"d09758d0-a546-4640-baa6-c1ade32bf891\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"768a21c7-9b5c-4ced-ada9-455c6de34eee\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":68,\"left\":80,\"width\":144,\"height\":14,\"fontSize\":10,\"content\":\"\\\"natural quality natural cow\",\"originalFontSize\":10,\"originalContent\":\"\u300a\u81ea\u7136\u54c1\u8d28\u81ea\u7136\u725b\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"right\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#4b4b49\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":6,\"refId\":\"7d6ad462-c9bc-4551-9174-c4f8acb5b6ff\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"7d6ad462-c9bc-4551-9174-c4f8acb5b6ff\",\"type\":\"image\",\"top\":67.5,\"left\":112,\"width\":88,\"height\":23,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/8b952925-723b-481f-85fe-1ab29c094d3a.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=1Fa9Gx1pZup2oWau3m2eNB1peNw%3D\",\"zIndex\":6,\"refId\":\"768a21c7-9b5c-4ced-ada9-455c6de34eee\"},{\"id\":\"0319f7bf-c475-40f1-b567-4893a6696702\",\"type\":\"image\",\"top\":400,\"left\":400,\"width\":800,\"height\":800,\"scaleX\":1,\"scaleY\":1,\"selectable\":false,\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"src\":\"https:\/\/akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com\/tmp\/4.JPG\",\"zIndex\":14,\"label\":\"original\"}],\"width\":800,\"height\":800,\"backgroundColor\":\"transparent\"}"},"requestId":"4d7b7715a5421814c1f05d2da8136cd9"}
        //https://ykt-rt.yiketu.com/tmp/days/360/user_translate_img/result_img/0/20250829/793ec3ff4d4b1cd50a9accf3ba61243d.jpg
        
        */

        String[] srcImages = new String[] {
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/10.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/11.png",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/12.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/13.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/14.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/15.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/16.png",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/17.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/18.png"
        };
        for(String srcImage : srcImages) {
            System.out.println("srcImage: " + srcImage);
            System.out.println("result: " + yiKeTuTranslateTest.getTranslateImg(
                appKey, 
                appSecret,
                srcImage));
                System.out.println("");
        }


    }

    public YiKeTuTranslateTest() {
    }

    
    public String getTranslateImg(String appKey, String appSecret, String imgUrl) {
        
        /*

        https://open-api.yiketu.com/gw/translate_img/translateImg
        
        请求参数
        参数	必选	类型	说明
        imgUrl	是	string	图片要求：不超过 4000*4000px；大小不超过 10MB；
        支持png、jpeg、jpg、bmp、webp。
        sourceLanguage	是	string	源语言编码（详见配置接口）
        targetLanguage	是	string	目标语言编码（详见配置接口）
        isTranslateProductText	否	int	是否翻译商品主体上文字，0或1，默认0
        isTranslateBrandText	否	int	是否翻译品牌上文字，0或1，默认0

        返回参数
        参数名称	类型	说明
        translateImgUrl	string	翻译图片链接（链接有效期7天，请自行转存）
        imageEditor	string	翻译图片详情数据，用于编辑翻译翻译图片时，传入SDK

        返回值示例
        {"result":"fail","code":-1,"reason":"\u5e94\u7528\u4f59\u989d\u4e0d\u8db3","requestId":"6296c4dbc62c56e5fe56f50d12a3b7fb"}
        {"result":"success","code":200,"data":{"translateImgUrl":"https:\/\/ykt-rt.yiketu.com\/tmp\/days\/360\/user_translate_img\/result_img\/0\/20250829\/793ec3ff4d4b1cd50a9accf3ba61243d.jpg","imageEditor":"{\"id\":\"127f1ba6-84c2-11f0-afc5-00163e198bc2\",\"language\":\"en\",\"type\":\"template\",\"layers\":[{\"type\":\"image\",\"top\":400,\"left\":400,\"width\":800,\"height\":800,\"scaleX\":1,\"scaleY\":1,\"selectable\":false,\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"d48c05af-f7fc-48c3-91ac-4b4772feea00\",\"src\":\"https:\/\/aib-image.oss-ap-southeast-1.aliyuncs.com\/tufan%2F122a64bc-84c2-11f0-a091-00163e198bc2.png?OSSAccessKeyId=LTAI5tSEGjGp5wixZgHLc3bV&Expires=4973134951&Signature=9MFzcSHgGAoeSg0n68x%2FtPFS%2BbM%3D\",\"zIndex\":1},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"42ba78d8-9651-481c-8b8f-1d992be6c513\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":729,\"left\":97,\"width\":124,\"height\":126,\"fontSize\":58,\"content\":\"Buy now\",\"originalFontSize\":58,\"originalContent\":\"\u7acb\u5373 \u62a2\u8d2d\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"right\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#fdfdfd\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":2,\"refId\":\"f4f9e163-236a-4c43-99e3-fdd93754bfa8\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"f4f9e163-236a-4c43-99e3-fdd93754bfa8\",\"type\":\"image\",\"top\":722.5,\"left\":96.5,\"width\":151,\"height\":153,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/c2e67419-5890-4438-901f-1dc9eb38f5ba.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=D1TjFUExZ4AdsqauTi96J9qnM%2B4%3D\",\"zIndex\":2,\"refId\":\"42ba78d8-9651-481c-8b8f-1d992be6c513\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"01063a4d-c2f6-47c9-a52e-0647dd7bf89b\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":765,\"left\":507,\"width\":560,\"height\":36,\"fontSize\":21,\"content\":\"3.6g\/100ml milk protein, original and good nutrition\",\"originalFontSize\":21,\"originalContent\":\"3.6g\/100mL\u4e73\u86cb\u767d \u539f\u751f\u597d\u8425\u517b\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#323024\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":5,\"refId\":\"d4d571db-86cf-4200-a88c-d472d4002bf8\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"d4d571db-86cf-4200-a88c-d472d4002bf8\",\"type\":\"image\",\"top\":765,\"left\":506.5,\"width\":575,\"height\":60,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/43a40500-a8f1-472a-a4e8-41bbd5b7806a.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=kbXI7WJ6dzowUU9lZHoHD5jFFyQ%3D\",\"zIndex\":5,\"refId\":\"01063a4d-c2f6-47c9-a52e-0647dd7bf89b\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"a9c1ed96-b536-4389-a96b-9f16be41b4f9\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":697,\"left\":473,\"width\":486,\"height\":24,\"fontSize\":23,\"content\":\"Royal beef pure milk 160ml*12 bags*1 box\",\"originalFontSize\":23,\"originalContent\":\"\u7687\u6c0f\u6c34\u725b\u7eaf\u725b\u5976160mL*12\u888b*1\u7bb1\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#2f3124\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":4,\"refId\":\"83932442-0b25-49b4-abc5-827b51a9d7c9\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"83932442-0b25-49b4-abc5-827b51a9d7c9\",\"type\":\"image\",\"top\":696.5,\"left\":473,\"width\":414,\"height\":39,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/d0c4007f-cd10-4ad2-b541-94806e4fc56e.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=Tz6w24YPBbzzcEUBdieG0j4uQVc%3D\",\"zIndex\":4,\"refId\":\"a9c1ed96-b536-4389-a96b-9f16be41b4f9\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"98dab7e5-329b-4bcd-af93-d6df0c7cd7c5\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":161,\"left\":403,\"width\":550,\"height\":52,\"fontSize\":25,\"content\":\"Portable bag with delicious taste at any time\",\"originalFontSize\":25,\"originalContent\":\"\u4fbf\u643a\u888b\u88c5 \u7f8e\u5473\u968f\u65f6\u4eab\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#000000\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":3,\"refId\":\"476da98c-3261-4c82-91de-9a97931a1e61\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"476da98c-3261-4c82-91de-9a97931a1e61\",\"type\":\"image\",\"top\":161,\"left\":403.5,\"width\":573,\"height\":84,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/e776f78a-490a-4198-9066-b0bc1f65cbf2.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=8iPk9YhiYEnMbqhiGe2vXBsqKdw%3D\",\"zIndex\":3,\"refId\":\"98dab7e5-329b-4bcd-af93-d6df0c7cd7c5\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"d09758d0-a546-4640-baa6-c1ade32bf891\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":601,\"left\":235,\"width\":316,\"height\":14,\"fontSize\":13,\"content\":\"Net content: 160ml\",\"originalFontSize\":13,\"originalContent\":\"\u51c0\u542b\u91cf\uff1a160mL\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#50514c\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":7,\"refId\":\"860fed32-0d18-4910-bb7a-e60fc2d84595\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"860fed32-0d18-4910-bb7a-e60fc2d84595\",\"type\":\"image\",\"top\":600.5,\"left\":235,\"width\":68,\"height\":23,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/1d3ffa6a-f86b-4a9c-8ff0-e8ce252766de.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=ru8hFlPcZvwuEOftXe247XQS008%3D\",\"zIndex\":7,\"refId\":\"d09758d0-a546-4640-baa6-c1ade32bf891\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"768a21c7-9b5c-4ced-ada9-455c6de34eee\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":68,\"left\":80,\"width\":144,\"height\":14,\"fontSize\":10,\"content\":\"\\\"natural quality natural cow\",\"originalFontSize\":10,\"originalContent\":\"\u300a\u81ea\u7136\u54c1\u8d28\u81ea\u7136\u725b\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"right\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#4b4b49\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":6,\"refId\":\"7d6ad462-c9bc-4551-9174-c4f8acb5b6ff\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"7d6ad462-c9bc-4551-9174-c4f8acb5b6ff\",\"type\":\"image\",\"top\":67.5,\"left\":112,\"width\":88,\"height\":23,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/8b952925-723b-481f-85fe-1ab29c094d3a.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=1Fa9Gx1pZup2oWau3m2eNB1peNw%3D\",\"zIndex\":6,\"refId\":\"768a21c7-9b5c-4ced-ada9-455c6de34eee\"},{\"id\":\"0319f7bf-c475-40f1-b567-4893a6696702\",\"type\":\"image\",\"top\":400,\"left\":400,\"width\":800,\"height\":800,\"scaleX\":1,\"scaleY\":1,\"selectable\":false,\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"src\":\"https:\/\/akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com\/tmp\/4.JPG\",\"zIndex\":14,\"label\":\"original\"}],\"width\":800,\"height\":800,\"backgroundColor\":\"transparent\"}"},"requestId":"4d7b7715a5421814c1f05d2da8136cd9"}
        
        源语言及对应目标语言列表
		"sourceLanguageList": [
			{
				"language": "zh",
				"languageName": "简体中文",
				"targetLanguageList": [
					{
						"language": "zh-tw",
						"languageName": "繁体中文"
					},
					{
						"language": "en",
						"languageName": "英语"
					},
					{
						"language": "es",
						"languageName": "西班牙语"
					},
					{
						"language": "fr",
						"languageName": "法语"
					},
					{
						"language": "pt",
						"languageName": "葡萄牙语"
					},
					{
						"language": "ko",
						"languageName": "韩语"
					},
					{
						"language": "ja",
						"languageName": "日语"
					},
					{
						"language": "ru",
						"languageName": "俄罗斯语"
					},
					{
						"language": "vi",
						"languageName": "越南语"
					},
					{
						"language": "ar",
						"languageName": "阿拉伯语"
					},
					{
						"language": "he",
						"languageName": "希伯来语"
					},
					{
						"language": "th",
						"languageName": "泰语"
					},
					{
						"language": "id",
						"languageName": "印尼语"
					},
					{
						"language": "de",
						"languageName": "德语"
					},
					{
						"language": "tr",
						"languageName": "土耳其语"
					},
					{
						"language": "it",
						"languageName": "意大利语"
					},
					{
						"language": "nl",
						"languageName": "荷兰语"
					},
					{
						"language": "pl",
						"languageName": "波兰语"
					},
					{
						"language": "uk",
						"languageName": "乌克兰语"
					},
					{
						"language": "ms",
						"languageName": "马来西亚语"
					}
				]
			},
			{
				"language": "en",
				"languageName": "英语",
				"targetLanguageList": [
					{
						"language": "zh-tw",
						"languageName": "繁体中文"
					},
					{
						"language": "zh",
						"languageName": "简体中文"
					},
					{
						"language": "es",
						"languageName": "西班牙语"
					},
					{
						"language": "fr",
						"languageName": "法语"
					},
					{
						"language": "pt",
						"languageName": "葡萄牙语"
					},
					{
						"language": "ko",
						"languageName": "韩语"
					},
					{
						"language": "ja",
						"languageName": "日语"
					},
					{
						"language": "ru",
						"languageName": "俄罗斯语"
					},
					{
						"language": "vi",
						"languageName": "越南语"
					},
					{
						"language": "ar",
						"languageName": "阿拉伯语"
					},
					{
						"language": "he",
						"languageName": "希伯来语"
					},
					{
						"language": "th",
						"languageName": "泰语"
					},
					{
						"language": "id",
						"languageName": "印尼语"
					},
					{
						"language": "de",
						"languageName": "德语"
					},
					{
						"language": "tr",
						"languageName": "土耳其语"
					},
					{
						"language": "it",
						"languageName": "意大利语"
					},
					{
						"language": "nl",
						"languageName": "荷兰语"
					},
					{
						"language": "pl",
						"languageName": "波兰语"
					},
					{
						"language": "uk",
						"languageName": "乌克兰语"
					},
					{
						"language": "ms",
						"languageName": "马来西亚语"
					}
				]
			},
			{
				"language": "tr",
				"languageName": "土耳其语",
				"targetLanguageList": [
					{
						"language": "en",
						"languageName": "英语"
					},
					{
						"language": "ar",
						"languageName": "阿拉伯语"
					}
				]
			}
		]
        */

        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(15, TimeUnit.SECONDS)
                    .writeTimeout(15, TimeUnit.SECONDS)
                    .build();
            //RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),json);
            String url = "https://open-api.yiketu.com/gw/translate_img/translateImg";
            Map<String, Object> params = new TreeMap<>();
            params.put("timestamp", Long.toString(System.currentTimeMillis()/1000));
            params.put("appKey", appKey);
            params.put("imgUrl", imgUrl); //图片要求：不超过 4000*4000px；大小不超过 10MB；支持png、jpeg、jpg、bmp、webp。
            params.put("sourceLanguage", "zh"); //原语言编码
            params.put("targetLanguage", "en"); //目标语言编
            params.put("isTranslateProductText", "0"); //是否翻译商品主体上文字，0或1，默认0
            params.put("isTranslateBrandText", "0"); //是否翻译品牌上文字，0或1，默认0

            params.put("sign", sign(params, appSecret));
            
            RequestBody body = mapToPostFormBody(params);
            Headers headers = new Headers.Builder()
                    .set("Content-Encoding", "UTF-8")
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(body)
                    .build();
            Call call = client.newCall(request);
            Response response = call.execute();
            if(response.isSuccessful()) {
                
            }
            String responseStr = new String(response.body().bytes(), "UTF-8");
            //System.out.println(responseStr);
            //{"result":"fail","code":-1,"reason":"\u5e94\u7528\u4f59\u989d\u4e0d\u8db3","requestId":"6296c4dbc62c56e5fe56f50d12a3b7fb"}
            //{"result":"success","code":200,"data":{"translateImgUrl":"https:\/\/ykt-rt.yiketu.com\/tmp\/days\/360\/user_translate_img\/result_img\/0\/20250829\/793ec3ff4d4b1cd50a9accf3ba61243d.jpg","imageEditor":"{\"id\":\"127f1ba6-84c2-11f0-afc5-00163e198bc2\",\"language\":\"en\",\"type\":\"template\",\"layers\":[{\"type\":\"image\",\"top\":400,\"left\":400,\"width\":800,\"height\":800,\"scaleX\":1,\"scaleY\":1,\"selectable\":false,\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"d48c05af-f7fc-48c3-91ac-4b4772feea00\",\"src\":\"https:\/\/aib-image.oss-ap-southeast-1.aliyuncs.com\/tufan%2F122a64bc-84c2-11f0-a091-00163e198bc2.png?OSSAccessKeyId=LTAI5tSEGjGp5wixZgHLc3bV&Expires=4973134951&Signature=9MFzcSHgGAoeSg0n68x%2FtPFS%2BbM%3D\",\"zIndex\":1},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"42ba78d8-9651-481c-8b8f-1d992be6c513\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":729,\"left\":97,\"width\":124,\"height\":126,\"fontSize\":58,\"content\":\"Buy now\",\"originalFontSize\":58,\"originalContent\":\"\u7acb\u5373 \u62a2\u8d2d\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"right\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#fdfdfd\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":2,\"refId\":\"f4f9e163-236a-4c43-99e3-fdd93754bfa8\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"f4f9e163-236a-4c43-99e3-fdd93754bfa8\",\"type\":\"image\",\"top\":722.5,\"left\":96.5,\"width\":151,\"height\":153,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/c2e67419-5890-4438-901f-1dc9eb38f5ba.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=D1TjFUExZ4AdsqauTi96J9qnM%2B4%3D\",\"zIndex\":2,\"refId\":\"42ba78d8-9651-481c-8b8f-1d992be6c513\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"01063a4d-c2f6-47c9-a52e-0647dd7bf89b\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":765,\"left\":507,\"width\":560,\"height\":36,\"fontSize\":21,\"content\":\"3.6g\/100ml milk protein, original and good nutrition\",\"originalFontSize\":21,\"originalContent\":\"3.6g\/100mL\u4e73\u86cb\u767d \u539f\u751f\u597d\u8425\u517b\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#323024\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":5,\"refId\":\"d4d571db-86cf-4200-a88c-d472d4002bf8\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"d4d571db-86cf-4200-a88c-d472d4002bf8\",\"type\":\"image\",\"top\":765,\"left\":506.5,\"width\":575,\"height\":60,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/43a40500-a8f1-472a-a4e8-41bbd5b7806a.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=kbXI7WJ6dzowUU9lZHoHD5jFFyQ%3D\",\"zIndex\":5,\"refId\":\"01063a4d-c2f6-47c9-a52e-0647dd7bf89b\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"a9c1ed96-b536-4389-a96b-9f16be41b4f9\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":697,\"left\":473,\"width\":486,\"height\":24,\"fontSize\":23,\"content\":\"Royal beef pure milk 160ml*12 bags*1 box\",\"originalFontSize\":23,\"originalContent\":\"\u7687\u6c0f\u6c34\u725b\u7eaf\u725b\u5976160mL*12\u888b*1\u7bb1\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#2f3124\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":4,\"refId\":\"83932442-0b25-49b4-abc5-827b51a9d7c9\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"83932442-0b25-49b4-abc5-827b51a9d7c9\",\"type\":\"image\",\"top\":696.5,\"left\":473,\"width\":414,\"height\":39,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/d0c4007f-cd10-4ad2-b541-94806e4fc56e.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=Tz6w24YPBbzzcEUBdieG0j4uQVc%3D\",\"zIndex\":4,\"refId\":\"a9c1ed96-b536-4389-a96b-9f16be41b4f9\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"98dab7e5-329b-4bcd-af93-d6df0c7cd7c5\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":161,\"left\":403,\"width\":550,\"height\":52,\"fontSize\":25,\"content\":\"Portable bag with delicious taste at any time\",\"originalFontSize\":25,\"originalContent\":\"\u4fbf\u643a\u888b\u88c5 \u7f8e\u5473\u968f\u65f6\u4eab\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#000000\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":3,\"refId\":\"476da98c-3261-4c82-91de-9a97931a1e61\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"476da98c-3261-4c82-91de-9a97931a1e61\",\"type\":\"image\",\"top\":161,\"left\":403.5,\"width\":573,\"height\":84,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/e776f78a-490a-4198-9066-b0bc1f65cbf2.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=8iPk9YhiYEnMbqhiGe2vXBsqKdw%3D\",\"zIndex\":3,\"refId\":\"98dab7e5-329b-4bcd-af93-d6df0c7cd7c5\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"d09758d0-a546-4640-baa6-c1ade32bf891\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":601,\"left\":235,\"width\":316,\"height\":14,\"fontSize\":13,\"content\":\"Net content: 160ml\",\"originalFontSize\":13,\"originalContent\":\"\u51c0\u542b\u91cf\uff1a160mL\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"center\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#50514c\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":7,\"refId\":\"860fed32-0d18-4910-bb7a-e60fc2d84595\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"860fed32-0d18-4910-bb7a-e60fc2d84595\",\"type\":\"image\",\"top\":600.5,\"left\":235,\"width\":68,\"height\":23,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/1d3ffa6a-f86b-4a9c-8ff0-e8ce252766de.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=ru8hFlPcZvwuEOftXe247XQS008%3D\",\"zIndex\":7,\"refId\":\"d09758d0-a546-4640-baa6-c1ade32bf891\"},{\"visible\":true,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"id\":\"768a21c7-9b5c-4ced-ada9-455c6de34eee\",\"type\":\"text\",\"splitByGrapheme\":false,\"direction\":\"ltr\",\"top\":68,\"left\":80,\"width\":144,\"height\":14,\"fontSize\":10,\"content\":\"\\\"natural quality natural cow\",\"originalFontSize\":10,\"originalContent\":\"\u300a\u81ea\u7136\u54c1\u8d28\u81ea\u7136\u725b\",\"scaleX\":1,\"scaleY\":1,\"fontFamily\":\"AlibabaSans-Regular\",\"textAlign\":\"right\",\"fontStyle\":\"normal\",\"fontWeight\":\"normal\",\"color\":\"#4b4b49\",\"backgroundColor\":\"transparent\",\"textDecoration\":\"none\",\"zIndex\":6,\"refId\":\"7d6ad462-c9bc-4551-9174-c4f8acb5b6ff\"},{\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":true,\"id\":\"7d6ad462-c9bc-4551-9174-c4f8acb5b6ff\",\"type\":\"image\",\"top\":67.5,\"left\":112,\"width\":88,\"height\":23,\"scaleX\":1,\"scaleY\":1,\"src\":\"https:\/\/imagine-oss.oss-accelerate.aliyuncs.com\/compose\/8b952925-723b-481f-85fe-1ab29c094d3a.png?OSSAccessKeyId=LTAI5tR9CxJh5q35LYbhGAeT&Expires=4973134953&Signature=1Fa9Gx1pZup2oWau3m2eNB1peNw%3D\",\"zIndex\":6,\"refId\":\"768a21c7-9b5c-4ced-ada9-455c6de34eee\"},{\"id\":\"0319f7bf-c475-40f1-b567-4893a6696702\",\"type\":\"image\",\"top\":400,\"left\":400,\"width\":800,\"height\":800,\"scaleX\":1,\"scaleY\":1,\"selectable\":false,\"visible\":false,\"opacity\":1,\"rotate\":0,\"flipX\":false,\"flipY\":false,\"locked\":false,\"src\":\"https:\/\/akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com\/tmp\/4.JPG\",\"zIndex\":14,\"label\":\"original\"}],\"width\":800,\"height\":800,\"backgroundColor\":\"transparent\"}"},"requestId":"4d7b7715a5421814c1f05d2da8136cd9"}
            
            return (String) ((Map)new Gson().fromJson(responseStr, Map.class).get("data")).get("translateImgUrl");
            //return responseStr;
        } catch (Exception e) {
            System.out.println("imgUrl: " + imgUrl + " error:" + e.getMessage());
            //e.printStackTrace();
        }
        return null;
    }



    public RequestBody mapToPostFormBody(Map<String, Object> params) {
        FormBody.Builder builder = new FormBody.Builder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            builder.add(entry.getKey(), entry.getValue().toString());
        }
        return builder.build();
    }

    /**
     * 生成签名
     *
     * @param params 请求参数（包含公共参数与业务参数）
     * @param appSecret 应用密钥
     * @return 签名值
     */
    public String sign(Map<String, Object> params, String appSecret) {
        if (params == null || appSecret == null) {
            throw new IllegalArgumentException("参数和密钥不能为空");
        }

        // 1. 移除sign字段（如果存在）
        Map<String, Object> filteredParams = new TreeMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (!"sign".equals(entry.getKey())) {
                filteredParams.put(entry.getKey(), entry.getValue());
            }
        }

        // 2. 按照参数名（key）进行ASCII升序排列（TreeMap自动排序）
        // 3. 拼接参数名和参数值
        StringBuilder signString = new StringBuilder();
        signString.append(appSecret); // 头部拼接appSecret

        for (Map.Entry<String, Object> entry : filteredParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 处理不同类型的值
            String valueStr;
            if (value instanceof Boolean) {
                valueStr = value.toString().toLowerCase(); // true/false
            } else {
                valueStr = value != null ? value.toString() : "";
            }

            signString.append(key).append(valueStr);
        }

        signString.append(appSecret); // 尾部拼接appSecret

        // 4. MD5加密并转大写
        return md5ToUpperCase(signString.toString());
    }

    /**
     * MD5加密并转为大写
     *
     * @param input 输入字符串
     * @return MD5加密后的大写字符串
     */
    private String md5ToUpperCase(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes("UTF-8"));

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }


}
