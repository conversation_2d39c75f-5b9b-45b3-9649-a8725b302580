package com.cashop.translate.client.ghostcut;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.RequestStatusEnum;
import com.google.gson.Gson;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 鬼手剪辑翻译客户端
 * 
 * <AUTHOR>
 */
@Component
public class GhostCutTranslateClient {

    private static final Logger logger = LoggerFactory.getLogger(GhostCutTranslateClient.class);

    @Value("${translate.ghostcut.app-key:}")
    private String appKey;

    @Value("${translate.ghostcut.app-secret:}")
    private String appSecret;

    @Value("${translate.ghostcut.base-url:https://api.zhaoli.com}")
    private String baseUrl;

    private final OkHttpClient httpClient;
    private final Gson gson = new Gson();

    public GhostCutTranslateClient() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 检查客户端是否可用
     */
    public boolean isAvailable() {
        return StringUtils.hasText(appKey) && StringUtils.hasText(appSecret);
    }

    /**
     * 同步图片翻译 - 鬼手剪辑是异步的，这里只提交任务不等待结果
     */
    public CompletableFuture<TranslateResponse> translateImageSync(TranslateRequest request) {
        logger.info("开始调用鬼手剪辑同步图片翻译，requestId: {}", request.getRequestId());

        // 直接提交任务，不等待结果
        return submitTranslateTask(request);
    }

    /**
     * 异步批量图片翻译 - 鬼手剪辑不支持批量翻译
     */
    public CompletableFuture<TranslateResponse> translateImageBatch(TranslateRequest request) {
        logger.warn("鬼手剪辑不支持批量翻译，requestId: {}", request.getRequestId());
        return CompletableFuture.completedFuture(
            buildErrorResponse(request, "鬼手剪辑不支持批量翻译"));
    }

    /**
     * 获取批量翻译结果 - 用于查询单个任务的结果
     */
    public CompletableFuture<TranslateResponse> getBatchTranslateResult(String taskId) {
        logger.info("开始查询鬼手剪辑翻译结果，taskId: {}", taskId);
        return queryTranslateResult(taskId, taskId);
    }

    /**
     * 同步文本翻译 - 鬼手剪辑不支持文本翻译
     */
    public CompletableFuture<TranslateResponse> translateTextSync(TranslateRequest request) {
        logger.warn("鬼手剪辑不支持文本翻译，requestId: {}", request.getRequestId());
        return CompletableFuture.completedFuture(
            buildErrorResponse(request, "鬼手剪辑不支持文本翻译"));
    }

    /**
     * 异步批量文本翻译 - 鬼手剪辑不支持文本翻译
     */
    public CompletableFuture<TranslateResponse> translateTextBatch(TranslateRequest request) {
        logger.warn("鬼手剪辑不支持批量文本翻译，requestId: {}", request.getRequestId());
        return CompletableFuture.completedFuture(
            buildErrorResponse(request, "鬼手剪辑不支持批量文本翻译"));
    }

    /**
     * 提交翻译任务
     */
    public CompletableFuture<TranslateResponse> submitTranslateTask(TranslateRequest request) {
        logger.info("提交鬼手剪辑翻译任务，requestId: {}", request.getRequestId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String url = baseUrl + "/v-w-c/gateway/ve/image/translate";
                
                // 获取第一个图片URL（鬼手剪辑只支持单张图片）
                String imageUrl = (request.getImageUrls() != null && !request.getImageUrls().isEmpty())
                    ? request.getImageUrls().get(0) : "";

                // 构建请求JSON
                String requestJson = String.format(
                    "{\"srcLang\":\"%s\",\"tgtLang\":\"%s\",\"synthesisOn\":1,\"translateOn\":1,\"commodityFilterOn\":1,\"downloadInfo\":\"{\\\"url\\\":\\\"%s\\\"}\"}",
                    getGhostCutLanguageCode(request.getSourceLanguage()),
                    getGhostCutLanguageCode(request.getTargetLanguage()),
                    imageUrl
                );
                
                byte[] requestBytes = requestJson.getBytes(StandardCharsets.UTF_8);
                String bodyMd5Hex = DigestUtils.md5DigestAsHex(requestBytes);
                String appSign = DigestUtils.md5DigestAsHex((bodyMd5Hex + appSecret).getBytes());

                RequestBody body = RequestBody.create(requestBytes, MediaType.parse("application/json; charset=utf-8"));
                Headers headers = new Headers.Builder()
                        .set("Content-Type", "application/json")
                        .set("Content-Encoding", "UTF-8")
                        .set("AppKey", appKey)
                        .set("AppSign", appSign)
                        .build();

                Request httpRequest = new Request.Builder()
                        .url(url)
                        .headers(headers)
                        .post(body)
                        .build();

                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    String responseStr = response.body().string();
                    logger.debug("鬼手剪辑提交任务响应: {}", responseStr);
                    
                    return parseSubmitResponse(request, responseStr);
                }
                
            } catch (Exception e) {
                logger.error("鬼手剪辑提交翻译任务失败，requestId: {}", request.getRequestId(), e);
                return buildErrorResponse(request, "提交翻译任务失败: " + e.getMessage());
            }
        });
    }

    /**
     * 查询翻译结果
     */
    public CompletableFuture<TranslateResponse> queryTranslateResult(String taskId, String requestId) {
        logger.info("查询鬼手剪辑翻译结果，taskId: {}, requestId: {}", taskId, requestId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String url = baseUrl + "/v-w-c/gateway/ve/image/translate/query";
                
                String requestJson = String.format("{\"id\":\"%s\"}", taskId);
                byte[] requestBytes = requestJson.getBytes(StandardCharsets.UTF_8);
                String bodyMd5Hex = DigestUtils.md5DigestAsHex(requestBytes);
                String appSign = DigestUtils.md5DigestAsHex((bodyMd5Hex + appSecret).getBytes());

                RequestBody body = RequestBody.create(requestBytes, MediaType.parse("application/json; charset=utf-8"));
                Headers headers = new Headers.Builder()
                        .set("Content-Type", "application/json")
                        .set("Content-Encoding", "UTF-8")
                        .set("AppKey", appKey)
                        .set("AppSign", appSign)
                        .build();

                Request httpRequest = new Request.Builder()
                        .url(url)
                        .headers(headers)
                        .post(body)
                        .build();

                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    String responseStr = response.body().string();
                    logger.debug("鬼手剪辑查询结果响应: {}", responseStr);
                    
                    return parseQueryResponse(requestId, taskId, responseStr);
                }
                
            } catch (Exception e) {
                logger.error("鬼手剪辑查询翻译结果失败，taskId: {}, requestId: {}", taskId, requestId, e);
                TranslateResponse result = new TranslateResponse();
                result.setRequestId(requestId);
                result.setTaskId(taskId);
                result.setSuccess(false);
                result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                result.setErrorMessage("查询翻译结果失败: " + e.getMessage());
                return result;
            }
        });
    }

    /**
     * 解析提交任务响应
     */
    private TranslateResponse parseSubmitResponse(TranslateRequest request, String responseStr) {
        TranslateResponse result = new TranslateResponse();
        result.setRequestId(request.getRequestId());
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = gson.fromJson(responseStr, Map.class);
            
            Double code = (Double) responseMap.get("code");
            String msg = (String) responseMap.get("msg");
            
            if (code != null && code.intValue() == 1000 && "success".equals(msg)) {
                Object body = responseMap.get("body");
                if (body instanceof Double) {
                    String taskId = String.valueOf(((Double) body).longValue());
                    
                    result.setSuccess(true);
                    result.setTaskId(taskId);
                    result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                    result.setRequestStatus(RequestStatusEnum.PROCESSING.getCode()); // 异步处理中
                    
                    logger.info("鬼手剪辑任务提交成功，requestId: {}, taskId: {}", 
                            request.getRequestId(), taskId);
                } else {
                    result.setSuccess(false);
                    result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                    result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                    result.setErrorMessage("响应数据格式错误");
                }
            } else {
                result.setSuccess(false);
                result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                result.setErrorMessage("任务提交失败: " + msg);
                
                logger.warn("鬼手剪辑任务提交失败，requestId: {}, 错误信息: {}", 
                        request.getRequestId(), msg);
            }
            
        } catch (Exception e) {
            logger.error("解析鬼手剪辑提交响应失败，requestId: {}", request.getRequestId(), e);
            result.setSuccess(false);
            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("响应解析失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 解析查询结果响应
     */
    private TranslateResponse parseQueryResponse(String requestId, String taskId, String responseStr) {
        TranslateResponse result = new TranslateResponse();
        result.setRequestId(requestId);
        result.setTaskId(taskId);
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = gson.fromJson(responseStr, Map.class);
            
            Double code = (Double) responseMap.get("code");
            String msg = (String) responseMap.get("msg");
            
            if (code != null && code.intValue() == 1000 && "success".equals(msg)) {
                @SuppressWarnings("unchecked")
                Map<String, Object> body = (Map<String, Object>) responseMap.get("body");
                if (body != null) {
                    Double status = (Double) body.get("status");
                    
                    if (status != null && status.intValue() == 1) {
                        // 处理成功
                        String resultJson = (String) body.get("result");
                        if (StringUtils.hasText(resultJson)) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> resultData = gson.fromJson(resultJson, Map.class);
                            String outputUrl = (String) resultData.get("output_url");
                            
                            result.setSuccess(true);
                            result.setCloudApiStatus(RequestStatusEnum.SUCCESS.getCode());
                            result.setCloudApiAsyncStatus(RequestStatusEnum.SUCCESS.getCode());
                            result.setRequestStatus(RequestStatusEnum.SUCCESS.getCode());
                            result.setTranslateImageSyncResultUrl(outputUrl);
                            
                            logger.info("鬼手剪辑翻译成功，requestId: {}, taskId: {}, 翻译结果URL: {}", 
                                    requestId, taskId, outputUrl);
                        } else {
                            result.setSuccess(false);
                            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                            result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
                            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                            result.setErrorMessage("翻译结果为空");
                        }
                    } else if (status != null && status.intValue() == -1) {
                        // 待处理
                        result.setSuccess(false);
                        result.setCloudApiStatus(RequestStatusEnum.PROCESSING.getCode());
                        result.setCloudApiAsyncStatus(RequestStatusEnum.PROCESSING.getCode());
                        result.setRequestStatus(RequestStatusEnum.PROCESSING.getCode());
                        result.setErrorMessage("翻译任务处理中");
                    } else {
                        // 处理失败
                        result.setSuccess(false);
                        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                        result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
                        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                        result.setErrorMessage("翻译任务处理失败");
                    }
                } else {
                    result.setSuccess(false);
                    result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                    result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
                    result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                    result.setErrorMessage("响应数据为空");
                }
            } else {
                result.setSuccess(false);
                result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
                result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
                result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
                result.setErrorMessage("查询失败: " + msg);

                logger.warn("鬼手剪辑查询失败，requestId: {}, taskId: {}, 错误信息: {}",
                        requestId, taskId, msg);
            }

        } catch (Exception e) {
            logger.error("解析鬼手剪辑查询响应失败，requestId: {}, taskId: {}", requestId, taskId, e);
            result.setSuccess(false);
            result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
            result.setCloudApiAsyncStatus(RequestStatusEnum.FAILED.getCode());
            result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
            result.setErrorMessage("响应解析失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 构建错误响应
     */
    private TranslateResponse buildErrorResponse(TranslateRequest request, String errorMessage) {
        TranslateResponse result = new TranslateResponse();
        result.setRequestId(request.getRequestId());
        result.setSuccess(false);
        result.setCloudApiStatus(RequestStatusEnum.FAILED.getCode());
        result.setRequestStatus(RequestStatusEnum.FAILED.getCode());
        result.setErrorMessage(errorMessage);
        return result;
    }

    /**
     * 转换语言代码为鬼手剪辑支持的格式
     */
    private String getGhostCutLanguageCode(String languageCode) {
        if (!StringUtils.hasText(languageCode)) {
            return "zh"; // 默认中文
        }
        
        // 鬼手剪辑支持的语言代码映射
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
            case "chinese":
                return "zh";
            case "zh-tw":
            case "zh-hant":
            case "traditional-chinese":
                return "zh-hant";
            case "en":
            case "english":
                return "en";
            case "ja":
            case "japanese":
                return "ja";
            case "ko":
            case "korean":
                return "ko";
            case "th":
            case "thai":
                return "th";
            case "vi":
            case "vietnamese":
                return "vi";
            case "id":
            case "indonesian":
                return "id";
            case "ms":
            case "malay":
                return "ms";
            case "hi":
            case "hindi":
                return "hi";
            case "ru":
            case "russian":
                return "ru";
            case "de":
            case "german":
                return "de";
            case "fr":
            case "french":
                return "fr";
            case "ar":
            case "arabic":
                return "ar";
            case "es":
            case "spanish":
                return "es";
            case "pt":
            case "portuguese":
                return "pt";
            case "it":
            case "italian":
                return "it";
            case "pl":
            case "polish":
                return "pl";
            case "auto":
                return "auto";
            default:
                logger.warn("不支持的语言代码: {}, 使用默认中文", languageCode);
                return "zh";
        }
    }
}
