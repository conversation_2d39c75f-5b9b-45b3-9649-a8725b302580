package com.cashop.translate.client.config;

import com.aliyun.sdk.service.alimt20181012.AsyncClient;
import com.aliyun.sdk.service.alimt20181012.DefaultAsyncClient;
import com.aliyun.sdk.service.alimt20181012.DefaultAsyncClientBuilder;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云客户端配置
 * 
 * <AUTHOR>
 */
@Configuration
public class AliyunClientConfig {

    private static final Logger logger = LoggerFactory.getLogger(AliyunClientConfig.class);

    @Autowired
    private AliyunConfig aliyunConfig;

    /**
     * 创建阿里云机器翻译异步客户端
     */
    @Bean
    public AsyncClient aliyunTranslateAsyncClient() {
        try {
            // 使用StaticCredentialProvider方式初始化
            StaticCredentialProvider provider = StaticCredentialProvider.create(
                    Credential.builder()
                            .accessKeyId(aliyunConfig.getAccessKeyId())
                            .accessKeySecret(aliyunConfig.getAccessKeySecret())
                            .build()
            );

            AsyncClient client = new DefaultAsyncClientBuilder()
                    .credentialsProvider(provider)
                    .region(aliyunConfig.getRegion())
                    .build();

            logger.info("阿里云翻译客户端初始化成功，区域: {}", aliyunConfig.getRegion());
            return client;
        } catch (Exception e) {
            logger.error("阿里云翻译客户端初始化失败", e);
            throw new RuntimeException("阿里云翻译客户端初始化失败", e);
        }
    }
}
