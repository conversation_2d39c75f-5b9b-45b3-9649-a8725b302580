package com.cashop.translate.client.ghostcut;

import okhttp3.*;
import okio.ByteString;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import com.google.gson.Gson;

import java.nio.charset.Charset;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 鬼手剪辑-图片翻译 只支持单张翻译，需要异步查询翻译结果
 */
public class GhostCutTranslateTest {

    private static final Logger logger = LoggerFactory.getLogger(GhostCutTranslateTest.class);

    
    public static void main(String[] args) throws Exception{

        String appKey = "e8d732890c194fee8a167ccb23d46d10";
        String appSecret = "e50b179761c6454ab91add516a124c07";

        GhostCutTranslateTest ghostCutTranslateTest = new GhostCutTranslateTest();

        /* 
        //1 提交翻译任务-异步
        String submitResult = ghostCutTranslateTest.getTranslateImg(
            appKey, 
            appSecret, 
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/1.JPG");
        //{"body":24928159,"code":1000,"count":0,"msg":"success","trace":"e1f9a54930e2441884858221a615aa93"}
        String id = submitResult.split(",")[0].split(":")[1]; //查询id
        Thread.sleep(10 * 1000);//等待5秒后查询结果
        //2 获取翻译结果
        System.out.println(ghostCutTranslateTest.getTranslateImgResult(
            appKey, 
            appSecret, 
            id));
        */
        //{"body":{"app":"API","callback":"","commodityFilterOn":1,"company":"423dc85e89cc4ac5","ctime":1756467205136,"deleted":0,"downloadInfo":"{\"url\":\"https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/1.JPG\"}","examineStatus":"DEFAULT","extraOptions":"","id":24928159,"idProject":*********,"isFreeTrial":0,"lutime":1756467210148,"ossDeleted":0,"paidPoint":0.2500,"priority":55,"result":"{\"meta_data\": [{\"ocr_region\": {\"confidence\": 0.9990234375, \"text\": \"\\u5f20\\u592a\\u548c\", \"text_region\": [[332, 11], [468, 11], [468, 55], [332, 55]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#fefdfd\", \"bg_color\": \"#ba010e\", \"stroke_color\": \"#b9050f\", \"alignment\": \"center\", \"font_size\": 44, \"trans_font_size\": 33, \"stroke_width\": 1}, \"target_width\": 227, \"target_height\": 44, \"image_size\": [800, 800], \"available_bound\": {\"left\": 270, \"right\": 526, \"up\": 0, \"down\": 55}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u5f20\\u592a\\u548c\", \"translation\": \"Taihe Zhang\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Taihe Zhang\", \"text_width\": 204.0, \"anchor_point\": [298, 45], \"is_rotate\": false, \"textbox\": {\"left\": 297.0, \"top\": 19.875, \"width\": 204.0, \"height\": 35.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9969482421875, \"text\": \"\\u81f3\\u81fb\\u4e4b\\u9009\", \"text_region\": [[535, 29], [616, 29], [616, 49], [535, 49]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#faebee\", \"bg_color\": \"#ba010c\", \"stroke_color\": \"#b9050e\", \"alignment\": \"left\", \"font_size\": 20, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 145, \"target_height\": 72, \"image_size\": [800, 800], \"available_bound\": {\"left\": 472, \"right\": 800, \"up\": 0, \"down\": 109}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u81f3\\u81fb\\u4e4b\\u9009\", \"translation\": \"The best choice\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"The best\", \"text_width\": 86.0, \"anchor_point\": [535, 35], \"is_rotate\": false, \"textbox\": {\"left\": 534.0, \"top\": 19.5, \"width\": 88.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"choice\", \"text_width\": 63.0, \"anchor_point\": [535, 58], \"is_rotate\": false, \"textbox\": {\"left\": 534.0, \"top\": 42.5, \"width\": 64.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9989013671875, \"text\": \"\\u767e\\u5e74\\u592a\\u548c\", \"text_region\": [[173, 28], [262, 27], [262, 48], [173, 49]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#f9f2f0\", \"bg_color\": \"#b5010c\", \"stroke_color\": \"#b4030a\", \"alignment\": \"left\", \"font_size\": 20, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 139, \"target_height\": 75, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 327, \"up\": 0, \"down\": 108}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u767e\\u5e74\\u592a\\u548c\", \"translation\": \"Centennial Taihe\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Centennial\", \"text_width\": 109.0, \"anchor_point\": [173, 34], \"is_rotate\": false, \"textbox\": {\"left\": 173.0, \"top\": 18.5, \"width\": 109.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"Taihe\", \"text_width\": 55.0, \"anchor_point\": [173, 57], \"is_rotate\": false, \"textbox\": {\"left\": 172.0, \"top\": 41.5, \"width\": 56.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.965478515625, \"text\": \"\\\"\\u672c\\u8349\\u597d\\u7269\\u6e05\\u6de1\\u597d\\u559d\\\"\", \"text_region\": [[125, 113], [672, 113], [672, 175], [125, 175]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#222022\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fefefe\", \"alignment\": \"center\", \"font_size\": 62, \"trans_font_size\": 29, \"stroke_width\": 1}, \"target_width\": 718, \"target_height\": 87, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 800, \"up\": 67, \"down\": 176}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\\"\\u672c\\u8349\\u597d\\u7269\\u6e05\\u6de1\\u597d\\u559d\\\"\", \"translation\": \"\\\"Herbal good things are light and delicious\\\"\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"\\\"Herbal good things are light and delicious\\\"\", \"text_width\": 636.0, \"anchor_point\": [80, 155], \"is_rotate\": false, \"textbox\": {\"left\": 80.0, \"top\": 132.375, \"width\": 635.0, \"height\": 31.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9978841145833334, \"text\": \"\\u91d1\\u94f6\\u82b135g*2\\u74f6\", \"text_region\": [[310, 189], [489, 189], [489, 222], [310, 222]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#0b080c\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fbfbfb\", \"alignment\": \"center\", \"font_size\": 33, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 322, \"target_height\": 56, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 800, \"up\": 181, \"down\": 275}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u91d1\\u94f6\\u82b135g*2\\u74f6\", \"translation\": \"Honeysuckle 35g * 2 bottles\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Honeysuckle 35g * 2 bottles\", \"text_width\": 274.0, \"anchor_point\": [262, 212], \"is_rotate\": false, \"textbox\": {\"left\": 262.0, \"top\": 196.5, \"width\": 275.0, \"height\": 22.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9180501302083334, \"text\": \"\\u51c0\\u542b\\u91cf36g\", \"text_region\": [[522, 611], [556, 611], [556, 627], [522, 627]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#e6d5b3\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fcfcfa\", \"alignment\": \"right\", \"font_size\": 16, \"trans_font_size\": 13, \"stroke_width\": 1}, \"target_width\": 61, \"target_height\": 35, \"image_size\": [800, 800], \"available_bound\": {\"left\": 441, \"right\": 572, \"up\": 611, \"down\": 800}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u51c0\\u542b\\u91cf36g\", \"translation\": \"Net content 36g\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Net\", \"text_width\": 25.0, \"anchor_point\": [531, 624], \"is_rotate\": false, \"textbox\": {\"left\": 531.0, \"top\": 613.375, \"width\": 26.0, \"height\": 12.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"content\", \"text_width\": 53.0, \"anchor_point\": [503, 639], \"is_rotate\": false, \"textbox\": {\"left\": 502.0, \"top\": 628.375, \"width\": 55.0, \"height\": 12.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"36g\", \"text_width\": 22.0, \"anchor_point\": [534, 654], \"is_rotate\": false, \"textbox\": {\"left\": 533.0, \"top\": 643.375, \"width\": 24.0, \"height\": 15.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.998046875, \"text\": \"\\u5f20\\u592a\\u548c\", \"text_region\": [[520, 465], [557, 465], [557, 481], [520, 481]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#382729\", \"bg_color\": \"#fcfcfc\", \"stroke_color\": \"#f9f9f8\", \"alignment\": \"center\", \"font_size\": 16, \"trans_font_size\": 13, \"stroke_width\": 1}, \"target_width\": 66, \"target_height\": 47, \"image_size\": [800, 800], \"available_bound\": {\"left\": 438, \"right\": 800, \"up\": 52, \"down\": 492}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u5f20\\u592a\\u548c\", \"translation\": \"Taihe Zhang\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Taihe\", \"text_width\": 37.0, \"anchor_point\": [520, 470], \"is_rotate\": false, \"textbox\": {\"left\": 519.0, \"top\": 458.375, \"width\": 39.0, \"height\": 13.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"Zhang\", \"text_width\": 42.0, \"anchor_point\": [517, 485], \"is_rotate\": false, \"textbox\": {\"left\": 516.0, \"top\": 473.375, \"width\": 44.0, \"height\": 16.0}, \"font_family\": \"Noto Sans\"}]}], \"output_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__output.jpg\", \"output_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__output.jpg\", \"inpaint_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__resized_inpaint.jpg\", \"inpaint_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__resized_inpaint.jpg\", \"commodity_mask_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__resized__commodity_mask.jpg\", \"commodity_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__resized__commodity_mask.jpg\"}","srcLang":"zh","status":1,"synthesisOn":1,"taskStatusEnum":{"code":1,"description":"处理成功","descriptionEn":"Successful processing","descriptionPt":"Processamento bem sucedido"},"tgtLang":"en","translateOn":1,"uid":"423dc85e89cc4ac5a3c3af22dfa29f7d"},"code":1000,"count":0,"msg":"success"}
        //https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24928159__1756467206049__output.jpg
        

        String[] srcImages = new String[] {

            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/10.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/11.png",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/12.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/13.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/14.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/15.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/16.png",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/17.jpg",
            "https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/18.png"
        };
        for(String srcImage : srcImages) {
            System.out.println("srcImage: " + srcImage);

                //1 提交翻译任务-异步
            String submitResult = ghostCutTranslateTest.getTranslateImg(
                appKey, 
                appSecret, 
                srcImage);
            //{"body":24928159,"code":1000,"count":0,"msg":"success","trace":"e1f9a54930e2441884858221a615aa93"}
            String id = submitResult.split(",")[0].split(":")[1]; //查询id
            Thread.sleep(10 * 1000);//等待5秒后查询结果
            //2 获取翻译结果
            System.out.println("result: " + ghostCutTranslateTest.getTranslateImgResult(
                appKey, 
                appSecret, 
                id,
                srcImage));

        }

    }

    public String getTranslateImg(String appKey, String appSecret, String imgUrl) {
        /*
        创建图片翻译任务
        地址 https://api.zhaoli.com/v-w-c/gateway/ve/image/translate
        请求方式 POST
        图片大小 不超过2000*2000，且不超过50M
        图片格式 png、jpeg、jpg、bmp、webp
        其他限制 URL地址中不能包含中文字符

        请求参数
        srcLang String 翻译选项--源语言
        tgtLang String 翻译选项--目标语言
        downloadInfo String 需要处理的图片URL(JSON String格式) 示例值：{\"url\":\"https://gc100.cdn.izhaoli.cn/ve_material_image/A-a0b03034fcbd457c/69ef5776186441bf827ea38a02eb63b6/1715408410239.png\"}
        synthesisOn Integer 是否开启图片合成(合成最终图片)  0否 1是
        translateOn Integer  是否开启翻译 0：否 1：是 （传0表示不进行翻译，仅自动擦除文字）
        commodityFilterOn Integer 是否开启商品文字保护，0不开启 1是开启（当开启时，商品上的文字会被保护，不会被擦除和翻译）
        callback String 回调地址url （如果传了回调地址则我方会主动将同获取图片擦除与翻译的处理结果结构一致的JSON数据POST到这个地址）
        extraOptions String JSON字符串，额外的剪辑配置

        响应参数
        body Long 任务id

        响应示例
        {"body":24928159,"code":1000,"count":0,"msg":"success","trace":"e1f9a54930e2441884858221a615aa93"}

        源语言及对应目标语言列表（is_ocr_support表示是否支持srcLang，is_translate_support表示是否支持tgtLang）
        {
            "zh": {
                "chinese_name": "中文",
                "english_name": "Chinese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "zh-hant": {
                "chinese_name": "中文(繁体)",
                "english_name": "Chinese (Traditional)",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "both": {
                "chinese_name": "中英文",
                "english_name": "Both Chinese and English",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "en": {
                "chinese_name": "英语",
                "english_name": "English",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "auto": {
                "chinese_name": "自动识别",
                "english_name": "auto",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ja": {
                "chinese_name": "日语",
                "english_name": "Japanese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ko": {
                "chinese_name": "韩语",
                "english_name": "Korean",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "th": {
                "chinese_name": "泰语",
                "english_name": "Thai",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "vi": {
                "chinese_name": "越南语",
                "english_name": "Vietnamese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "id": {
                "chinese_name": "印尼语",
                "english_name": "Indonesian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "jv": {
                "chinese_name": "印尼-爪哇语",
                "english_name": "Indonesian-Javanese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ms": {
                "chinese_name": "马来语（马来西亚）",
                "english_name": "Malay (Malaysian)",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "fil": {
                "chinese_name": "菲律宾语",
                "english_name": "Philippines",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "hi": {
                "chinese_name": "印地语",
                "english_name": "Hindi",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ru": {
                "chinese_name": "俄语",
                "english_name": "Russian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "de": {
                "chinese_name": "德语",
                "english_name": "German",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "fr": {
                "chinese_name": "法语",
                "english_name": "French",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ar": {
                "chinese_name": "阿拉伯语",
                "english_name": "Arabic",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "es": {
                "chinese_name": "西班牙语",
                "english_name": "Spanish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "pt": {
                "chinese_name": "葡萄牙语",
                "english_name": "Portuguese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "it": {
                "chinese_name": "意大利语",
                "english_name": "Italy",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "pl": {
                "chinese_name": "波兰语",
                "english_name": "Polish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "da": {
                "chinese_name": "丹麦语",
                "english_name": "Danish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "fi": {
                "chinese_name": "芬兰语",
                "english_name": "Finnish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "km": {
                "chinese_name": "高棉语",
                "english_name": "Khmer",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "la": {
                "chinese_name": "拉丁语",
                "english_name": "Latin",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "nl": {
                "chinese_name": "荷兰语",
                "english_name": "Dutch",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ti": {
                "chinese_name": "藏语",
                "english_name": "Tibetan",
                "is_ocr_support": false,
                "is_translate_support": true
            },
            "uy": {
                "chinese_name": "维吾尔语",
                "english_name": "Uyghur",
                "is_ocr_support": false,
                "is_translate_support": true
            },
            "ga": {
                "chinese_name": "爱尔兰语",
                "english_name": "Irish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "et": {
                "chinese_name": "爱沙尼亚语",
                "english_name": "Estonia",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sq": {
                "chinese_name": "阿尔巴尼亚语",
                "english_name": "Albanian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "az": {
                "chinese_name": "阿塞拜疆语",
                "english_name": "Azerbaijan",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "be": {
                "chinese_name": "白俄罗斯语",
                "english_name": "Belarus",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mww": {
                "chinese_name": "白苗语",
                "english_name": "Bai Miao",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "bg": {
                "chinese_name": "保加利亚语",
                "english_name": "Bulgarian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "eu": {
                "chinese_name": "巴斯克语",
                "english_name": "Basque",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "is": {
                "chinese_name": "冰岛语",
                "english_name": "Icelandic",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "bs": {
                "chinese_name": "波斯尼亚语",
                "english_name": "Bosnian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "fy": {
                "chinese_name": "弗里斯兰语",
                "english_name": "Frisian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "jy": {
                "chinese_name": "格鲁吉亚语",
                "english_name": "Georgia",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "gu": {
                "chinese_name": "古吉拉特语",
                "english_name": "Gujarati",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ht": {
                "chinese_name": "海地克里奥尔语",
                "english_name": "Haiti Creole",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ha": {
                "chinese_name": "豪萨语",
                "english_name": "Hausa",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "gl": {
                "chinese_name": "加利西亚语",
                "english_name": "Galician",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ca": {
                "chinese_name": "加泰罗尼亚语（加泰隆语）",
                "english_name": "Catalan",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "cs": {
                "chinese_name": "捷克语",
                "english_name": "Czech",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ky": {
                "chinese_name": "吉尔吉斯语（柯尔克孜语）",
                "english_name": "Kyrgyz (Kirgiz)",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "otq": {
                "chinese_name": "克雷塔罗奥托米语",
                "english_name": "Queretaro Otomi",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "hr": {
                "chinese_name": "克罗地亚语",
                "english_name": "Croatian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "co": {
                "chinese_name": "科西嘉语",
                "english_name": "Corsican",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ku": {
                "chinese_name": "库尔德语",
                "english_name": "Kurdish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "lv": {
                "chinese_name": "拉脱维亚语",
                "english_name": "Latvia",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "lt": {
                "chinese_name": "立陶宛语",
                "english_name": "Lithuanian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ro": {
                "chinese_name": "罗马尼亚语",
                "english_name": "Romanian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "lb": {
                "chinese_name": "卢森堡语",
                "english_name": "Luxembourg",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "rw": {
                "chinese_name": "卢旺达语",
                "english_name": "Kinyarwanda",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mi": {
                "chinese_name": "毛利语",
                "english_name": "Maori",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mg": {
                "chinese_name": "马尔加什语",
                "english_name": "Malagasy",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mt": {
                "chinese_name": "马耳他语",
                "english_name": "Maltese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mr": {
                "chinese_name": "马拉地语",
                "english_name": "Marathi",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ml": {
                "chinese_name": "马拉雅拉姆语",
                "english_name": "Malayalam",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mk": {
                "chinese_name": "马其顿语",
                "english_name": "Macedonia",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "mn": {
                "chinese_name": "蒙古语",
                "english_name": "Mongolian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "bn": {
                "chinese_name": "孟加拉语",
                "english_name": "Bengali",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "my": {
                "chinese_name": "缅甸语",
                "english_name": "Burmese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "af": {
                "chinese_name": "南非荷兰语",
                "english_name": "Afrikaans",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "zu": {
                "chinese_name": "南非祖鲁语",
                "english_name": "South African Zulu",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ne": {
                "chinese_name": "尼泊尔语",
                "english_name": "Nepal",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "no": {
                "chinese_name": "挪威语",
                "english_name": "Norwegian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "pa": {
                "chinese_name": "旁遮普语",
                "english_name": "Punjabi",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ps": {
                "chinese_name": "普什图语",
                "english_name": "Pashto",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "kn": {
                "chinese_name": "卡纳达语",
                "english_name": "Kannada",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ny": {
                "chinese_name": "齐切瓦语",
                "english_name": "Chichewa",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sv": {
                "chinese_name": "瑞典语",
                "english_name": "Swedish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sr": {
                "chinese_name": "塞尔维亚语（拉丁语）",
                "english_name": "Serbia (Latin)",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "st": {
                "chinese_name": "塞索托语",
                "english_name": "Sesotho",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sm": {
                "chinese_name": "萨摩亚语",
                "english_name": "Samoa",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "eo": {
                "chinese_name": "世界语",
                "english_name": "Esperanto",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sk": {
                "chinese_name": "斯洛伐克语",
                "english_name": "Slovak",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sl": {
                "chinese_name": "斯洛文尼亚语",
                "english_name": "Slovenian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sw": {
                "chinese_name": "斯瓦希里语",
                "english_name": "Swahili",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "so": {
                "chinese_name": "索马里语",
                "english_name": "Somalia",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "gd": {
                "chinese_name": "苏格兰盖尔语",
                "english_name": "Scottish Gaelic",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ceb": {
                "chinese_name": "宿务语",
                "english_name": "Cebu language",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "te": {
                "chinese_name": "泰卢固语",
                "english_name": "Telugu",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "to": {
                "chinese_name": "汤加语",
                "english_name": "Tongan",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "tg": {
                "chinese_name": "塔吉克语",
                "english_name": "Tajik",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "tr": {
                "chinese_name": "土耳其语",
                "english_name": "Turkey",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "tk": {
                "chinese_name": "土库曼语",
                "english_name": "Turkmen",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "cy": {
                "chinese_name": "威尔士语",
                "english_name": "Welsh",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ur": {
                "chinese_name": "乌尔都语",
                "english_name": "Urdu",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "uk": {
                "chinese_name": "乌克兰语",
                "english_name": "Ukraine",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "uz": {
                "chinese_name": "乌兹别克语",
                "english_name": "Uzbek",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "haw": {
                "chinese_name": "夏威夷语",
                "english_name": "Hawaiian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "hu": {
                "chinese_name": "匈牙利语",
                "english_name": "Hungarian",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "sn": {
                "chinese_name": "修纳语",
                "english_name": "Shona",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "he": {
                "chinese_name": "希伯来",
                "english_name": "Hebrew",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "el": {
                "chinese_name": "希腊语",
                "english_name": "Greek",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "su": {
                "chinese_name": "印尼巽他语",
                "english_name": "Indonesian-Sundanese",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "ig": {
                "chinese_name": "伊博语",
                "english_name": "Igbo",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "yi": {
                "chinese_name": "意第绪语",
                "english_name": "Yiddish",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "yua": {
                "chinese_name": "尤卡坦玛雅语",
                "english_name": "Yucatan Mayan",
                "is_ocr_support": true,
                "is_translate_support": true
            },
            "yo": {
                "chinese_name": "约鲁巴语",
                "english_name": "Yoruba",
                "is_ocr_support": true,
                "is_translate_support": true
            }
        }
        */
        String requestStr = "{\"srcLang\":\"zh\",\"tgtLang\":\"en\",\"synthesisOn\":1,\"translateOn\":1,\"commodityFilterOn\":1,\"downloadInfo\":\"{\\\"url\\\":\\\"" + imgUrl + "\\\"}\"}";
        String url = "https://api.zhaoli.com/v-w-c/gateway/ve/image/translate";
        byte[] requestStrBytes = requestStr.getBytes(Charset.forName("UTF-8"));
        String bodyMd5Hex = DigestUtils.md5DigestAsHex(requestStrBytes);
        String md5Hex = DigestUtils.md5DigestAsHex((bodyMd5Hex + appSecret).getBytes());

        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(10, TimeUnit.SECONDS)
                    .writeTimeout(10, TimeUnit.SECONDS)
                    .build();
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),requestStrBytes);
            Headers headers = new Headers.Builder()
                    .set("Content-Type", "application/json")
                    .set("Content-Encoding", "UTF-8")
                    .set("AppKey", appKey)
                    .set("AppSign", md5Hex)
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(body)
                    .build();
            Call call = client.newCall(request);
            Response response = call.execute();
            String responseStr = response.body().string();
            //System.out.println(responseStr);
            return responseStr;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getTranslateImgResult(String appKey, String appSecret, String id, String imgUrl) {

        /* 
        查询图片翻译处理结果
        功能简述 本接口为异步接口，第二步。查询图片任务的处理结果。
        地址 https://api.zhaoli.com/v-w-c/gateway/ve/image/translate/query
        请求方式 POST

        请求参数
        id Long  图片翻译任务id

        响应参数
        body Object
            主要字段说明
            status 处理状态
                - -1：待处理
                - 1：处理成功
                - >1：处理失败
            result 图片处理结果数据(JSON String格式)，包括
                - output_url是包含翻译结果并合成后的图片；
                - inpaint_url是擦除后的无文字底图。
            taskStatusEnum 状态的信息
        
        响应示例
        {"body":{"app":"API","callback":"","commodityFilterOn":1,"company":"423dc85e89cc4ac5","ctime":1756705554601,"deleted":0,"downloadInfo":"{\"url\":\"https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/1.JPG\"}","examineStatus":"DEFAULT","extraOptions":"","id":24953847,"idProject":*********,"isFreeTrial":0,"lutime":1756705560282,"ossDeleted":0,"paidPoint":0.2500,"priority":55,"result":"{\"meta_data\": [{\"ocr_region\": {\"confidence\": 0.9990234375, \"text\": \"\\u5f20\\u592a\\u548c\", \"text_region\": [[332, 11], [468, 11], [468, 55], [332, 55]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#fefdfd\", \"bg_color\": \"#ba010e\", \"stroke_color\": \"#b9050f\", \"alignment\": \"center\", \"font_size\": 44, \"trans_font_size\": 33, \"stroke_width\": 1}, \"target_width\": 227, \"target_height\": 44, \"image_size\": [800, 800], \"available_bound\": {\"left\": 270, \"right\": 526, \"up\": 0, \"down\": 55}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u5f20\\u592a\\u548c\", \"translation\": \"Taihe Zhang\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Taihe Zhang\", \"text_width\": 204.0, \"anchor_point\": [298, 45], \"is_rotate\": false, \"textbox\": {\"left\": 297.0, \"top\": 19.875, \"width\": 204.0, \"height\": 35.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9969482421875, \"text\": \"\\u81f3\\u81fb\\u4e4b\\u9009\", \"text_region\": [[535, 29], [616, 29], [616, 49], [535, 49]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#fbf0f3\", \"bg_color\": \"#bb010c\", \"stroke_color\": \"#b9050e\", \"alignment\": \"left\", \"font_size\": 20, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 145, \"target_height\": 72, \"image_size\": [800, 800], \"available_bound\": {\"left\": 472, \"right\": 800, \"up\": 0, \"down\": 109}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u81f3\\u81fb\\u4e4b\\u9009\", \"translation\": \"The best choice\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"The best\", \"text_width\": 86.0, \"anchor_point\": [535, 35], \"is_rotate\": false, \"textbox\": {\"left\": 534.0, \"top\": 19.5, \"width\": 88.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"choice\", \"text_width\": 63.0, \"anchor_point\": [535, 58], \"is_rotate\": false, \"textbox\": {\"left\": 534.0, \"top\": 42.5, \"width\": 64.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9989013671875, \"text\": \"\\u767e\\u5e74\\u592a\\u548c\", \"text_region\": [[173, 28], [262, 27], [262, 48], [173, 49]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#f9f2f0\", \"bg_color\": \"#b5010c\", \"stroke_color\": \"#b4050c\", \"alignment\": \"left\", \"font_size\": 20, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 139, \"target_height\": 75, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 327, \"up\": 0, \"down\": 108}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u767e\\u5e74\\u592a\\u548c\", \"translation\": \"Centennial Taihe\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Centennial\", \"text_width\": 109.0, \"anchor_point\": [173, 34], \"is_rotate\": false, \"textbox\": {\"left\": 173.0, \"top\": 18.5, \"width\": 109.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"Taihe\", \"text_width\": 55.0, \"anchor_point\": [173, 57], \"is_rotate\": false, \"textbox\": {\"left\": 172.0, \"top\": 41.5, \"width\": 56.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.965478515625, \"text\": \"\\\"\\u672c\\u8349\\u597d\\u7269\\u6e05\\u6de1\\u597d\\u559d\\\"\", \"text_region\": [[125, 113], [672, 113], [672, 175], [125, 175]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#222022\", \"bg_color\": \"#fffefe\", \"stroke_color\": \"#fefefe\", \"alignment\": \"center\", \"font_size\": 62, \"trans_font_size\": 29, \"stroke_width\": 1}, \"target_width\": 718, \"target_height\": 87, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 800, \"up\": 67, \"down\": 176}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\\"\\u672c\\u8349\\u597d\\u7269\\u6e05\\u6de1\\u597d\\u559d\\\"\", \"translation\": \"\\\"Herbal good things are light and delicious\\\"\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"\\\"Herbal good things are light and delicious\\\"\", \"text_width\": 636.0, \"anchor_point\": [80, 155], \"is_rotate\": false, \"textbox\": {\"left\": 80.0, \"top\": 132.375, \"width\": 635.0, \"height\": 31.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9978841145833334, \"text\": \"\\u91d1\\u94f6\\u82b135g*2\\u74f6\", \"text_region\": [[310, 189], [489, 189], [489, 222], [310, 222]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#0c090c\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fbfbfb\", \"alignment\": \"center\", \"font_size\": 33, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 322, \"target_height\": 56, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 800, \"up\": 181, \"down\": 275}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u91d1\\u94f6\\u82b135g*2\\u74f6\", \"translation\": \"Honeysuckle 35g * 2 bottles\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Honeysuckle 35g * 2 bottles\", \"text_width\": 274.0, \"anchor_point\": [262, 212], \"is_rotate\": false, \"textbox\": {\"left\": 262.0, \"top\": 196.5, \"width\": 275.0, \"height\": 22.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9178059895833334, \"text\": \"\\u51c0\\u542b\\u91cf36g\", \"text_region\": [[522, 611], [556, 611], [556, 627], [522, 627]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#f4ede0\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fdfdfc\", \"alignment\": \"right\", \"font_size\": 16, \"trans_font_size\": 13, \"stroke_width\": 1}, \"target_width\": 61, \"target_height\": 35, \"image_size\": [800, 800], \"available_bound\": {\"left\": 441, \"right\": 572, \"up\": 611, \"down\": 800}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u51c0\\u542b\\u91cf36g\", \"translation\": \"Net content 36g\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Net\", \"text_width\": 25.0, \"anchor_point\": [531, 624], \"is_rotate\": false, \"textbox\": {\"left\": 531.0, \"top\": 613.375, \"width\": 26.0, \"height\": 12.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"content\", \"text_width\": 53.0, \"anchor_point\": [503, 639], \"is_rotate\": false, \"textbox\": {\"left\": 502.0, \"top\": 628.375, \"width\": 55.0, \"height\": 12.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"36g\", \"text_width\": 22.0, \"anchor_point\": [534, 654], \"is_rotate\": false, \"textbox\": {\"left\": 533.0, \"top\": 643.375, \"width\": 24.0, \"height\": 15.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.998046875, \"text\": \"\\u5f20\\u592a\\u548c\", \"text_region\": [[520, 465], [557, 465], [557, 481], [520, 481]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#847c7f\", \"bg_color\": \"#fcfcfc\", \"stroke_color\": \"#fafaf9\", \"alignment\": \"center\", \"font_size\": 16, \"trans_font_size\": 13, \"stroke_width\": 1}, \"target_width\": 66, \"target_height\": 47, \"image_size\": [800, 800], \"available_bound\": {\"left\": 438, \"right\": 800, \"up\": 52, \"down\": 492}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u5f20\\u592a\\u548c\", \"translation\": \"Taihe Zhang\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Taihe\", \"text_width\": 37.0, \"anchor_point\": [520, 470], \"is_rotate\": false, \"textbox\": {\"left\": 519.0, \"top\": 458.375, \"width\": 39.0, \"height\": 13.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"Zhang\", \"text_width\": 42.0, \"anchor_point\": [517, 485], \"is_rotate\": false, \"textbox\": {\"left\": 516.0, \"top\": 473.375, \"width\": 44.0, \"height\": 16.0}, \"font_family\": \"Noto Sans\"}]}], \"output_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__output.jpg\", \"output_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__output.jpg\", \"inpaint_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized_inpaint.jpg\", \"inpaint_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized_inpaint.jpg\", \"commodity_mask_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized__commodity_mask.jpg\", \"commodity_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized__commodity_mask.jpg\"}","srcLang":"zh","status":1,"synthesisOn":1,"taskStatusEnum":{"code":1,"description":"处理成功","descriptionEn":"Successful processing","descriptionPt":"Processamento bem sucedido"},"tgtLang":"en","translateOn":1,"uid":"423dc85e89cc4ac5a3c3af22dfa29f7d"},"code":1000,"count":0,"msg":"success"}
        {"body":{"app":"API","callback":"","commodityFilterOn":1,"company":"423dc85e89cc4ac5","ctime":1756705679459,"deleted":0,"downloadInfo":"{\"url\":\"https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/18.png\"}","examineStatus":"DEFAULT","extraOptions":"","id":24953873,"idProject":*********,"isFreeTrial":0,"lutime":1756705683571,"ossDeleted":0,"paidPoint":0.2500,"priority":55,"result":"{\"meta_data\": [], \"output_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\", \"output_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\", \"inpaint_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\", \"inpaint_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\"}","srcLang":"zh","status":1,"synthesisOn":1,"taskStatusEnum":{"code":1,"description":"处理成功","descriptionEn":"Successful processing","descriptionPt":"Processamento bem sucedido"},"tgtLang":"en","translateOn":1,"uid":"423dc85e89cc4ac5a3c3af22dfa29f7d"},"code":1000,"count":0,"msg":"success"}
        */

        String requestStr = "{\"id\":\""+id+"\"}";
        String url = "https://api.zhaoli.com/v-w-c/gateway/ve/image/translate/query";
        byte[] requestStrBytes = requestStr.getBytes(Charset.forName("UTF-8"));
        String bodyMd5Hex = DigestUtils.md5DigestAsHex(requestStrBytes);
        String md5Hex = DigestUtils.md5DigestAsHex((bodyMd5Hex + appSecret).getBytes());

        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(10, TimeUnit.SECONDS)
                    .writeTimeout(10, TimeUnit.SECONDS)
                    .build();
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),requestStrBytes);
            Headers headers = new Headers.Builder()
                    .set("Content-Type", "application/json")
                    .set("Content-Encoding", "UTF-8")
                    .set("AppKey", appKey)
                    .set("AppSign", md5Hex)
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .headers(headers)
                    .post(body)
                    .build();
            Call call = client.newCall(request);
            Response response = call.execute();
            String responseStr = response.body().string();
            //{"body":{"app":"API","callback":"","commodityFilterOn":1,"company":"423dc85e89cc4ac5","ctime":1756705554601,"deleted":0,"downloadInfo":"{\"url\":\"https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/1.JPG\"}","examineStatus":"DEFAULT","extraOptions":"","id":24953847,"idProject":*********,"isFreeTrial":0,"lutime":1756705560282,"ossDeleted":0,"paidPoint":0.2500,"priority":55,"result":"{\"meta_data\": [{\"ocr_region\": {\"confidence\": 0.9990234375, \"text\": \"\\u5f20\\u592a\\u548c\", \"text_region\": [[332, 11], [468, 11], [468, 55], [332, 55]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#fefdfd\", \"bg_color\": \"#ba010e\", \"stroke_color\": \"#b9050f\", \"alignment\": \"center\", \"font_size\": 44, \"trans_font_size\": 33, \"stroke_width\": 1}, \"target_width\": 227, \"target_height\": 44, \"image_size\": [800, 800], \"available_bound\": {\"left\": 270, \"right\": 526, \"up\": 0, \"down\": 55}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u5f20\\u592a\\u548c\", \"translation\": \"Taihe Zhang\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Taihe Zhang\", \"text_width\": 204.0, \"anchor_point\": [298, 45], \"is_rotate\": false, \"textbox\": {\"left\": 297.0, \"top\": 19.875, \"width\": 204.0, \"height\": 35.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9969482421875, \"text\": \"\\u81f3\\u81fb\\u4e4b\\u9009\", \"text_region\": [[535, 29], [616, 29], [616, 49], [535, 49]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#fbf0f3\", \"bg_color\": \"#bb010c\", \"stroke_color\": \"#b9050e\", \"alignment\": \"left\", \"font_size\": 20, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 145, \"target_height\": 72, \"image_size\": [800, 800], \"available_bound\": {\"left\": 472, \"right\": 800, \"up\": 0, \"down\": 109}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u81f3\\u81fb\\u4e4b\\u9009\", \"translation\": \"The best choice\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"The best\", \"text_width\": 86.0, \"anchor_point\": [535, 35], \"is_rotate\": false, \"textbox\": {\"left\": 534.0, \"top\": 19.5, \"width\": 88.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"choice\", \"text_width\": 63.0, \"anchor_point\": [535, 58], \"is_rotate\": false, \"textbox\": {\"left\": 534.0, \"top\": 42.5, \"width\": 64.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9989013671875, \"text\": \"\\u767e\\u5e74\\u592a\\u548c\", \"text_region\": [[173, 28], [262, 27], [262, 48], [173, 49]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#f9f2f0\", \"bg_color\": \"#b5010c\", \"stroke_color\": \"#b4050c\", \"alignment\": \"left\", \"font_size\": 20, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 139, \"target_height\": 75, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 327, \"up\": 0, \"down\": 108}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u767e\\u5e74\\u592a\\u548c\", \"translation\": \"Centennial Taihe\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Centennial\", \"text_width\": 109.0, \"anchor_point\": [173, 34], \"is_rotate\": false, \"textbox\": {\"left\": 173.0, \"top\": 18.5, \"width\": 109.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"Taihe\", \"text_width\": 55.0, \"anchor_point\": [173, 57], \"is_rotate\": false, \"textbox\": {\"left\": 172.0, \"top\": 41.5, \"width\": 56.0, \"height\": 17.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.965478515625, \"text\": \"\\\"\\u672c\\u8349\\u597d\\u7269\\u6e05\\u6de1\\u597d\\u559d\\\"\", \"text_region\": [[125, 113], [672, 113], [672, 175], [125, 175]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#222022\", \"bg_color\": \"#fffefe\", \"stroke_color\": \"#fefefe\", \"alignment\": \"center\", \"font_size\": 62, \"trans_font_size\": 29, \"stroke_width\": 1}, \"target_width\": 718, \"target_height\": 87, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 800, \"up\": 67, \"down\": 176}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\\"\\u672c\\u8349\\u597d\\u7269\\u6e05\\u6de1\\u597d\\u559d\\\"\", \"translation\": \"\\\"Herbal good things are light and delicious\\\"\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"\\\"Herbal good things are light and delicious\\\"\", \"text_width\": 636.0, \"anchor_point\": [80, 155], \"is_rotate\": false, \"textbox\": {\"left\": 80.0, \"top\": 132.375, \"width\": 635.0, \"height\": 31.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9978841145833334, \"text\": \"\\u91d1\\u94f6\\u82b135g*2\\u74f6\", \"text_region\": [[310, 189], [489, 189], [489, 222], [310, 222]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#0c090c\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fbfbfb\", \"alignment\": \"center\", \"font_size\": 33, \"trans_font_size\": 20, \"stroke_width\": 1}, \"target_width\": 322, \"target_height\": 56, \"image_size\": [800, 800], \"available_bound\": {\"left\": 0, \"right\": 800, \"up\": 181, \"down\": 275}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u91d1\\u94f6\\u82b135g*2\\u74f6\", \"translation\": \"Honeysuckle 35g * 2 bottles\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Honeysuckle 35g * 2 bottles\", \"text_width\": 274.0, \"anchor_point\": [262, 212], \"is_rotate\": false, \"textbox\": {\"left\": 262.0, \"top\": 196.5, \"width\": 275.0, \"height\": 22.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.9178059895833334, \"text\": \"\\u51c0\\u542b\\u91cf36g\", \"text_region\": [[522, 611], [556, 611], [556, 627], [522, 627]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#f4ede0\", \"bg_color\": \"#fefefe\", \"stroke_color\": \"#fdfdfc\", \"alignment\": \"right\", \"font_size\": 16, \"trans_font_size\": 13, \"stroke_width\": 1}, \"target_width\": 61, \"target_height\": 35, \"image_size\": [800, 800], \"available_bound\": {\"left\": 441, \"right\": 572, \"up\": 611, \"down\": 800}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u51c0\\u542b\\u91cf36g\", \"translation\": \"Net content 36g\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Net\", \"text_width\": 25.0, \"anchor_point\": [531, 624], \"is_rotate\": false, \"textbox\": {\"left\": 531.0, \"top\": 613.375, \"width\": 26.0, \"height\": 12.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"content\", \"text_width\": 53.0, \"anchor_point\": [503, 639], \"is_rotate\": false, \"textbox\": {\"left\": 502.0, \"top\": 628.375, \"width\": 55.0, \"height\": 12.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"36g\", \"text_width\": 22.0, \"anchor_point\": [534, 654], \"is_rotate\": false, \"textbox\": {\"left\": 533.0, \"top\": 643.375, \"width\": 24.0, \"height\": 15.0}, \"font_family\": \"Noto Sans\"}]}, {\"ocr_region\": {\"confidence\": 0.998046875, \"text\": \"\\u5f20\\u592a\\u548c\", \"text_region\": [[520, 465], [557, 465], [557, 481], [520, 481]], \"lang\": \"zh\", \"clip_size\": [800, 800]}, \"text_style\": {\"fill_color\": \"#847c7f\", \"bg_color\": \"#fcfcfc\", \"stroke_color\": \"#fafaf9\", \"alignment\": \"center\", \"font_size\": 16, \"trans_font_size\": 13, \"stroke_width\": 1}, \"target_width\": 66, \"target_height\": 47, \"image_size\": [800, 800], \"available_bound\": {\"left\": 438, \"right\": 800, \"up\": 52, \"down\": 492}, \"rotate90\": false, \"tgt_lang\": \"en\", \"source\": \"\\u5f20\\u592a\\u548c\", \"translation\": \"Taihe Zhang\", \"is_commodity\": false, \"render_infos\": [{\"text\": \"Taihe\", \"text_width\": 37.0, \"anchor_point\": [520, 470], \"is_rotate\": false, \"textbox\": {\"left\": 519.0, \"top\": 458.375, \"width\": 39.0, \"height\": 13.0}, \"font_family\": \"Noto Sans\"}, {\"text\": \"Zhang\", \"text_width\": 42.0, \"anchor_point\": [517, 485], \"is_rotate\": false, \"textbox\": {\"left\": 516.0, \"top\": 473.375, \"width\": 44.0, \"height\": 16.0}, \"font_family\": \"Noto Sans\"}]}], \"output_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__output.jpg\", \"output_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__output.jpg\", \"inpaint_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized_inpaint.jpg\", \"inpaint_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized_inpaint.jpg\", \"commodity_mask_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized__commodity_mask.jpg\", \"commodity_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953847__1756705555555__resized__commodity_mask.jpg\"}","srcLang":"zh","status":1,"synthesisOn":1,"taskStatusEnum":{"code":1,"description":"处理成功","descriptionEn":"Successful processing","descriptionPt":"Processamento bem sucedido"},"tgtLang":"en","translateOn":1,"uid":"423dc85e89cc4ac5a3c3af22dfa29f7d"},"code":1000,"count":0,"msg":"success"}
            //{"body":{"app":"API","callback":"","commodityFilterOn":1,"company":"423dc85e89cc4ac5","ctime":1756705679459,"deleted":0,"downloadInfo":"{\"url\":\"https://akc-file-upload-test.obs.cn-east-3.myhuaweicloud.com/tmp/18.png\"}","examineStatus":"DEFAULT","extraOptions":"","id":24953873,"idProject":*********,"isFreeTrial":0,"lutime":1756705683571,"ossDeleted":0,"paidPoint":0.2500,"priority":55,"result":"{\"meta_data\": [], \"output_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\", \"output_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\", \"inpaint_osskey\": \"ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\", \"inpaint_url\": \"https://gc100.cdn.izhaoli.cn/ve_image_translate_task/423dc85e89cc4ac5/task_24953873__1756705680364.png\"}","srcLang":"zh","status":1,"synthesisOn":1,"taskStatusEnum":{"code":1,"description":"处理成功","descriptionEn":"Successful processing","descriptionPt":"Processamento bem sucedido"},"tgtLang":"en","translateOn":1,"uid":"423dc85e89cc4ac5a3c3af22dfa29f7d"},"code":1000,"count":0,"msg":"success"}
            //System.out.println(responseStr);
            //return responseStr;
            // body/result/output_url
            return (String) new Gson().fromJson(((Map)new Gson().fromJson(responseStr, Map.class).get("body")).get("result").toString(), Map.class).get("output_url");        
        } catch (Exception e) {
            System.out.println("imgUrl: " + imgUrl + " error:" + e.getMessage());
            //e.printStackTrace();
        }
        return null;
    }


}
