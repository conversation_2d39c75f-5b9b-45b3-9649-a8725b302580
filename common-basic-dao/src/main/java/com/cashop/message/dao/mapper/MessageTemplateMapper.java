package com.cashop.message.dao.mapper;

import com.cashop.message.dao.entity.MessageTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息模版Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface MessageTemplateMapper {

    /**
     * 插入消息模版
     * 
     * @param messageTemplate 消息模版
     * @return 影响行数
     */
    int insert(MessageTemplate messageTemplate);

    /**
     * 根据ID查询消息模版
     * 
     * @param id 主键ID
     * @return 消息模版
     */
    MessageTemplate selectById(@Param("id") Long id);

    /**
     * 根据场景编码、语言和消息类型查询模版
     * 
     * @param sceneCode 场景编码
     * @param language 语言
     * @param messageType 消息类型
     * @return 消息模版
     */
    MessageTemplate selectBySceneAndLanguageAndType(@Param("sceneCode") String sceneCode, 
                                                   @Param("language") String language, 
                                                   @Param("messageType") String messageType);

    /**
     * 根据场景编码查询模版列表
     * 
     * @param sceneCode 场景编码
     * @return 消息模版列表
     */
    List<MessageTemplate> selectBySceneCode(@Param("sceneCode") String sceneCode);

    /**
     * 根据消息类型查询模版列表
     * 
     * @param messageType 消息类型
     * @return 消息模版列表
     */
    List<MessageTemplate> selectByMessageType(@Param("messageType") String messageType);

    /**
     * 更新消息模版
     * 
     * @param messageTemplate 消息模版
     * @return 影响行数
     */
    int update(MessageTemplate messageTemplate);

    /**
     * 根据ID删除消息模版
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询所有消息模版
     * 
     * @return 消息模版列表
     */
    List<MessageTemplate> selectAll();
}
