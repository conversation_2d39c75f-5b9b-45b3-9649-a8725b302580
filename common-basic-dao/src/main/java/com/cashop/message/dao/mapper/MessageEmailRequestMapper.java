package com.cashop.message.dao.mapper;

import com.cashop.message.dao.entity.MessageEmailRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邮件发送记录Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface MessageEmailRequestMapper {

    /**
     * 插入邮件发送记录
     * 
     * @param messageEmailRequest 邮件发送记录
     * @return 影响行数
     */
    int insert(MessageEmailRequest messageEmailRequest);

    /**
     * 根据ID查询邮件发送记录
     * 
     * @param id 主键ID
     * @return 邮件发送记录
     */
    MessageEmailRequest selectById(@Param("id") Long id);

    /**
     * 根据请求ID查询邮件发送记录
     * 
     * @param requestId 请求ID
     * @return 邮件发送记录
     */
    MessageEmailRequest selectByRequestId(@Param("requestId") String requestId);

    /**
     * 根据应用ID查询邮件发送记录列表
     * 
     * @param appId 应用ID
     * @return 邮件发送记录列表
     */
    List<MessageEmailRequest> selectByAppId(@Param("appId") String appId);

    /**
     * 根据场景编码查询邮件发送记录列表
     * 
     * @param sceneCode 场景编码
     * @return 邮件发送记录列表
     */
    List<MessageEmailRequest> selectBySceneCode(@Param("sceneCode") String sceneCode);

    /**
     * 根据发送状态查询邮件发送记录列表
     * 
     * @param sendStatus 发送状态
     * @return 邮件发送记录列表
     */
    List<MessageEmailRequest> selectBySendStatus(@Param("sendStatus") String sendStatus);

    /**
     * 查询需要重试的邮件发送记录
     * 
     * @return 邮件发送记录列表
     */
    List<MessageEmailRequest> selectRetryableRecords();

    /**
     * 更新邮件发送记录
     * 
     * @param messageEmailRequest 邮件发送记录
     * @return 影响行数
     */
    int update(MessageEmailRequest messageEmailRequest);

    /**
     * 更新发送状态
     * 
     * @param id 主键ID
     * @param sendStatus 发送状态
     * @param errorMessage 错误信息
     * @param currentRetryCount 当前重试次数
     * @return 影响行数
     */
    int updateSendStatus(@Param("id") Long id, 
                        @Param("sendStatus") String sendStatus, 
                        @Param("errorMessage") String errorMessage,
                        @Param("currentRetryCount") Integer currentRetryCount);

    /**
     * 更新实际发送内容
     * 
     * @param id 主键ID
     * @param actualTitle 实际发送标题
     * @param actualContent 实际发送内容
     * @return 影响行数
     */
    int updateActualContent(@Param("id") Long id, 
                           @Param("actualTitle") String actualTitle, 
                           @Param("actualContent") String actualContent);

    /**
     * 根据ID删除邮件发送记录
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
