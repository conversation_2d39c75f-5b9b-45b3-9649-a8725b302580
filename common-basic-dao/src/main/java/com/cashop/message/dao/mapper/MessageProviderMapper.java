package com.cashop.message.dao.mapper;

import com.cashop.message.dao.entity.MessageProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息供应商Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface MessageProviderMapper {

    /**
     * 插入消息供应商
     * 
     * @param messageProvider 消息供应商
     * @return 影响行数
     */
    int insert(MessageProvider messageProvider);

    /**
     * 根据ID查询消息供应商
     * 
     * @param id 主键ID
     * @return 消息供应商
     */
    MessageProvider selectById(@Param("id") Long id);

    /**
     * 根据消息类型和供应商Key查询
     * 
     * @param messageType 消息类型
     * @param providerKey 供应商Key
     * @return 消息供应商
     */
    MessageProvider selectByTypeAndKey(@Param("messageType") String messageType, 
                                      @Param("providerKey") String providerKey);

    /**
     * 根据消息类型查询供应商列表
     * 
     * @param messageType 消息类型
     * @return 消息供应商列表
     */
    List<MessageProvider> selectByMessageType(@Param("messageType") String messageType);

    /**
     * 根据供应商Key查询供应商列表
     * 
     * @param providerKey 供应商Key
     * @return 消息供应商列表
     */
    List<MessageProvider> selectByProviderKey(@Param("providerKey") String providerKey);

    /**
     * 更新消息供应商
     * 
     * @param messageProvider 消息供应商
     * @return 影响行数
     */
    int update(MessageProvider messageProvider);

    /**
     * 根据ID删除消息供应商
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询所有消息供应商
     * 
     * @return 消息供应商列表
     */
    List<MessageProvider> selectAll();
}
