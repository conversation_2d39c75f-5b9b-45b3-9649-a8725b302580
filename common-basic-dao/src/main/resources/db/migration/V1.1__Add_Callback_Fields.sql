-- 添加回调相关字段到翻译请求记录表
ALTER TABLE translate_request_record 
ADD COLUMN callback VARCHAR(500) COMMENT '回调地址',
ADD COLUMN callback_status INT DEFAULT 0 COMMENT '翻译结果回调状态：0-初始，1-成功，2-失败',
ADD COLUMN callback_response TEXT COMMENT '翻译结果回调结果',
ADD COLUMN callback_retry_count INT DEFAULT 0 COMMENT '翻译结果回调执行次数',
ADD COLUMN callback_max_retry_count INT DEFAULT 3 COMMENT '翻译结果回调最大执行次数，默认3';

-- 添加索引以提高查询性能
CREATE INDEX idx_callback_status ON translate_request_record(callback_status);
CREATE INDEX idx_callback_created_time ON translate_request_record(created_time);
CREATE INDEX idx_callback_query ON translate_request_record(callback_status, request_status, created_time);
