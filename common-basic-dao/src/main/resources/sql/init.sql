-- 翻译请求记录表
CREATE TABLE `translate_request_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求ID，用于去重和查询',
  `request_type` varchar(20) NOT NULL COMMENT '请求类型：SYNC_SINGLE(同步单张), ASYNC_BATCH(异步批量)',
  `provider` varchar(20) NOT NULL COMMENT '云服务提供商：ALIYUN, BAIDU, TENCENT, HUAWEI',
  `source_language` varchar(10) DEFAULT NULL COMMENT '源语言',
  `target_language` varchar(10) NOT NULL COMMENT '目标语言',
  `image_count` int(11) DEFAULT 1 COMMENT '图片数量',
  `request_params` text COMMENT '请求参数JSON',
  `request_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '请求状态：PENDING(待处理), PROCESSING(处理中), SUCCESS(成功), FAILED(失败)',
  `cloud_api_status` varchar(20) DEFAULT NULL COMMENT '云服务API状态：SUCCESS(成功), FAILED(失败)',
  `cloud_api_response` text COMMENT '云服务API响应JSON',
  `cloud_api_async_status` varchar(20) DEFAULT NULL COMMENT '异步查询结果-云服务API状态：SUCCESS(成功), FAILED(失败), PROCESSING(处理中)',
  `cloud_api_async_response` text COMMENT '异步查询结果-云服务API响应JSON',
  `task_id` varchar(64) DEFAULT NULL COMMENT '阿里云批量翻译任务ID',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `error_message` text COMMENT '错误信息',
  `image_sync_result_url` varchar(128) COMMENT '图片同步翻译结果URL',
  `image_batch_results` text COMMENT '图片批量翻译结果',
  `translated_text` text COMMENT '文本翻译结果',
  `callback` VARCHAR(500) COMMENT '回调地址',
  `callback_status` INT DEFAULT 0 COMMENT '翻译结果回调状态：0-初始，1-成功，2-失败',
  `callback_response` TEXT COMMENT '翻译结果回调结果',
  `callback_retry_count` INT DEFAULT 0 COMMENT '翻译结果回调执行次数',
  `callback_max_retry_count` INT DEFAULT 3 COMMENT '翻译结果回调最大执行次数，默认3',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_provider` (`provider`),
  KEY `idx_request_status` (`request_status`),
  KEY `idx_cloud_api_async_status` (`cloud_api_async_status`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_created_time` (`created_time`, `callback_status`, `request_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='翻译请求记录表';

-- 消息模版表
CREATE TABLE `message_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scene_code` varchar(64) NOT NULL COMMENT '场景编码',
  `language` varchar(10) NOT NULL DEFAULT 'en' COMMENT '语言',
  `message_type` varchar(20) NOT NULL COMMENT '消息类型：email/sms/push',
  `message_title` varchar(255) NOT NULL COMMENT '模版标题',
  `message_content` text NOT NULL COMMENT '模版内容',
  `message_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `retry_config` tinyint(1) DEFAULT 1 COMMENT '重试开关：0-关闭，1-开启',
  `provider_key` varchar(64) NOT NULL COMMENT '消息供应商：qq_email/google_email等',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scene_language_type` (`scene_code`, `language`, `message_type`),
  KEY `idx_scene_code` (`scene_code`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_provider_key` (`provider_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息模版表';

-- 消息供应商表
CREATE TABLE `message_provider` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `message_type` varchar(20) NOT NULL COMMENT '模版类型：email/sms/push',
  `provider_key` varchar(64) NOT NULL COMMENT '供应商名称',
  `provider_user_name` varchar(255) NOT NULL COMMENT '用户名',
  `provider_user_pwd` varchar(255) NOT NULL COMMENT '登录信息',
  `provider_config` text DEFAULT NULL COMMENT '供应商配置信息JSON',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_provider` (`message_type`, `provider_key`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_provider_key` (`provider_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息供应商表';

-- 邮件发送记录表
CREATE TABLE `message_email_request` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `scene_code` varchar(64) NOT NULL COMMENT '场景编码',
  `language` varchar(10) NOT NULL DEFAULT 'en' COMMENT '语言',
  `email_host` varchar(255) DEFAULT NULL COMMENT '邮件服务端host',
  `email_port` int(11) DEFAULT NULL COMMENT '邮件服务端port',
  `from_email` varchar(255) DEFAULT NULL COMMENT '发件人账号',
  `from_email_pwd` varchar(255) DEFAULT NULL COMMENT '发件人密码',
  `to_email_list` text NOT NULL COMMENT '接收人列表JSON',
  `cc_email_list` text DEFAULT NULL COMMENT '抄送人列表JSON',
  `attachment_list` text DEFAULT NULL COMMENT '附件列表JSON',
  `request_title` varchar(500) DEFAULT NULL COMMENT '请求标题',
  `template_params` text DEFAULT NULL COMMENT '模版参数JSON',
  `actual_title` varchar(500) DEFAULT NULL COMMENT '实际发送标题',
  `actual_content` text DEFAULT NULL COMMENT '实际发送内容',
  `current_retry_count` int(11) DEFAULT 0 COMMENT '当前执行次数',
  `max_retry_count` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `send_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '发送状态：PENDING/PROCESSING/SUCCESS/FAILED',
  `error_message` text DEFAULT NULL COMMENT '发送异常信息',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_scene_code` (`scene_code`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件发送记录表';
