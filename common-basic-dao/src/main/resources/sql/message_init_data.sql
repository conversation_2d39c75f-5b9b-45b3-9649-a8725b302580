-- 消息发送功能初始化数据

-- 插入消息供应商配置
INSERT INTO message_provider (message_type, provider_key, provider_user_name, provider_user_pwd, provider_config) VALUES
('email', 'qq_email', '<EMAIL>', 'Okmijn456123', '{"host":"smtp.exmail.qq.com","port":465,"ssl":true}'),
('email', 'google_email', '<EMAIL>', 'password123', '{"host":"smtp.gmail.com","port":587,"ssl":true}');

-- 插入消息模版
INSERT INTO message_template (scene_code, language, message_type, message_title, message_content, message_remark, retry_config, provider_key) VALUES
-- 用户注册验证码模版
('user_register', 'en', 'email', 'Registration Verification Code', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Registration Verification Code</title>
</head>
<body>
    <h1>Welcome to ${appName}!</h1>
    <p>Dear ${userName},</p>
    <p>Thank you for registering with us. Your verification code is: <strong>${verificationCode}</strong></p>
    <p>This code will expire in ${expireMinutes} minutes.</p>
    <p>If you did not request this code, please ignore this email.</p>
    <p>Best regards,<br>${appName} Team</p>
</body>
</html>', 
'用户注册验证码邮件模版', 1, 'qq_email'),

('user_register', 'zh', 'email', '注册验证码', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>注册验证码</title>
</head>
<body>
    <h1>欢迎注册${appName}！</h1>
    <p>亲爱的${userName}，</p>
    <p>感谢您注册我们的服务。您的验证码是：<strong>${verificationCode}</strong></p>
    <p>此验证码将在${expireMinutes}分钟后过期。</p>
    <p>如果您没有请求此验证码，请忽略此邮件。</p>
    <p>此致<br>${appName}团队</p>
</body>
</html>', 
'用户注册验证码邮件模版（中文）', 1, 'qq_email'),

-- 密码重置模版
('password_reset', 'en', 'email', 'Password Reset Request', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Password Reset Request</title>
</head>
<body>
    <h1>Password Reset Request</h1>
    <p>Dear ${userName},</p>
    <p>We received a request to reset your password. Your reset code is: <strong>${resetCode}</strong></p>
    <p>This code will expire in ${expireMinutes} minutes.</p>
    <p>If you did not request a password reset, please ignore this email.</p>
    <p>Best regards,<br>${appName} Team</p>
</body>
</html>', 
'密码重置邮件模版', 1, 'qq_email'),

('password_reset', 'zh', 'email', '密码重置请求', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>密码重置请求</title>
</head>
<body>
    <h1>密码重置请求</h1>
    <p>亲爱的${userName}，</p>
    <p>我们收到了您的密码重置请求。您的重置码是：<strong>${resetCode}</strong></p>
    <p>此重置码将在${expireMinutes}分钟后过期。</p>
    <p>如果您没有请求密码重置，请忽略此邮件。</p>
    <p>此致<br>${appName}团队</p>
</body>
</html>', 
'密码重置邮件模版（中文）', 1, 'qq_email'),

-- 系统通知模版
('system_notification', 'en', 'email', 'System Notification', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>System Notification</title>
</head>
<body>
    <h1>System Notification</h1>
    <p>Dear ${userName},</p>
    <p>${notificationContent}</p>
    <p>Time: ${notificationTime}</p>
    <p>Best regards,<br>${appName} Team</p>
</body>
</html>', 
'系统通知邮件模版', 1, 'qq_email'),

('system_notification', 'zh', 'email', '系统通知', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>系统通知</title>
</head>
<body>
    <h1>系统通知</h1>
    <p>亲爱的${userName}，</p>
    <p>${notificationContent}</p>
    <p>时间：${notificationTime}</p>
    <p>此致<br>${appName}团队</p>
</body>
</html>', 
'系统通知邮件模版（中文）', 1, 'qq_email');
