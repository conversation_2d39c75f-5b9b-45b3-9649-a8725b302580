<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.message.dao.mapper.MessageEmailRequestMapper">

    <resultMap id="BaseResultMap" type="com.cashop.message.dao.entity.MessageEmailRequest">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="scene_code" property="sceneCode" jdbcType="VARCHAR"/>
        <result column="language" property="language" jdbcType="VARCHAR"/>
        <result column="email_host" property="emailHost" jdbcType="VARCHAR"/>
        <result column="email_port" property="emailPort" jdbcType="INTEGER"/>
        <result column="from_email" property="fromEmail" jdbcType="VARCHAR"/>
        <result column="from_email_pwd" property="fromEmailPwd" jdbcType="VARCHAR"/>
        <result column="to_email_list" property="toEmailList" jdbcType="LONGVARCHAR"/>
        <result column="cc_email_list" property="ccEmailList" jdbcType="LONGVARCHAR"/>
        <result column="attachment_list" property="attachmentList" jdbcType="LONGVARCHAR"/>
        <result column="request_title" property="requestTitle" jdbcType="VARCHAR"/>
        <result column="template_params" property="templateParams" jdbcType="LONGVARCHAR"/>
        <result column="actual_title" property="actualTitle" jdbcType="VARCHAR"/>
        <result column="actual_content" property="actualContent" jdbcType="LONGVARCHAR"/>
        <result column="current_retry_count" property="currentRetryCount" jdbcType="INTEGER"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="send_status" property="sendStatus" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, request_id, app_id, scene_code, language, email_host, email_port, from_email, from_email_pwd,
        to_email_list, cc_email_list, attachment_list, request_title, template_params, actual_title, 
        actual_content, current_retry_count, max_retry_count, send_status, error_message, created_time, updated_time
    </sql>

    <insert id="insert" parameterType="com.cashop.message.dao.entity.MessageEmailRequest" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO message_email_request (
            request_id, app_id, scene_code, language, email_host, email_port, from_email, from_email_pwd,
            to_email_list, cc_email_list, attachment_list, request_title, template_params, actual_title,
            actual_content, current_retry_count, max_retry_count, send_status, error_message
        ) VALUES (
            #{requestId}, #{appId}, #{sceneCode}, #{language}, #{emailHost}, #{emailPort}, #{fromEmail}, #{fromEmailPwd},
            #{toEmailList}, #{ccEmailList}, #{attachmentList}, #{requestTitle}, #{templateParams}, #{actualTitle},
            #{actualContent}, #{currentRetryCount}, #{maxRetryCount}, #{sendStatus}, #{errorMessage}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_email_request
        WHERE id = #{id}
    </select>

    <select id="selectByRequestId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_email_request
        WHERE request_id = #{requestId}
    </select>

    <select id="selectByAppId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_email_request
        WHERE app_id = #{appId}
        ORDER BY created_time DESC
    </select>

    <select id="selectBySceneCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_email_request
        WHERE scene_code = #{sceneCode}
        ORDER BY created_time DESC
    </select>

    <select id="selectBySendStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_email_request
        WHERE send_status = #{sendStatus}
        ORDER BY created_time DESC
    </select>

    <select id="selectRetryableRecords" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_email_request
        WHERE send_status IN ('PENDING', 'FAILED') 
          AND current_retry_count &lt; max_retry_count
        ORDER BY created_time ASC
    </select>

    <update id="update" parameterType="com.cashop.message.dao.entity.MessageEmailRequest">
        UPDATE message_email_request
        SET app_id = #{appId},
            scene_code = #{sceneCode},
            language = #{language},
            email_host = #{emailHost},
            email_port = #{emailPort},
            from_email = #{fromEmail},
            from_email_pwd = #{fromEmailPwd},
            to_email_list = #{toEmailList},
            cc_email_list = #{ccEmailList},
            attachment_list = #{attachmentList},
            request_title = #{requestTitle},
            template_params = #{templateParams},
            actual_title = #{actualTitle},
            actual_content = #{actualContent},
            current_retry_count = #{currentRetryCount},
            max_retry_count = #{maxRetryCount},
            send_status = #{sendStatus},
            error_message = #{errorMessage}
        WHERE id = #{id}
    </update>

    <update id="updateSendStatus">
        UPDATE message_email_request
        SET send_status = #{sendStatus},
            error_message = #{errorMessage},
            current_retry_count = #{currentRetryCount}
        WHERE id = #{id}
    </update>

    <update id="updateActualContent">
        UPDATE message_email_request
        SET actual_title = #{actualTitle},
            actual_content = #{actualContent}
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM message_email_request WHERE id = #{id}
    </delete>

</mapper>
