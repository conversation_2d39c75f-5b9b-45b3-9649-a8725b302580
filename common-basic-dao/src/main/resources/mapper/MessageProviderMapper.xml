<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.message.dao.mapper.MessageProviderMapper">

    <resultMap id="BaseResultMap" type="com.cashop.message.dao.entity.MessageProvider">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="message_type" property="messageType" jdbcType="VARCHAR"/>
        <result column="provider_key" property="providerKey" jdbcType="VARCHAR"/>
        <result column="provider_user_name" property="providerUserName" jdbcType="VARCHAR"/>
        <result column="provider_user_pwd" property="providerUserPwd" jdbcType="VARCHAR"/>
        <result column="provider_config" property="providerConfig" jdbcType="LONGVARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, message_type, provider_key, provider_user_name, provider_user_pwd, 
        provider_config, created_time, updated_time
    </sql>

    <insert id="insert" parameterType="com.cashop.message.dao.entity.MessageProvider" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO message_provider (
            message_type, provider_key, provider_user_name, provider_user_pwd, provider_config
        ) VALUES (
            #{messageType}, #{providerKey}, #{providerUserName}, #{providerUserPwd}, #{providerConfig}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_provider
        WHERE id = #{id}
    </select>

    <select id="selectByTypeAndKey" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_provider
        WHERE message_type = #{messageType} AND provider_key = #{providerKey}
    </select>

    <select id="selectByMessageType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_provider
        WHERE message_type = #{messageType}
        ORDER BY created_time DESC
    </select>

    <select id="selectByProviderKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_provider
        WHERE provider_key = #{providerKey}
        ORDER BY created_time DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_provider
        ORDER BY created_time DESC
    </select>

    <update id="update" parameterType="com.cashop.message.dao.entity.MessageProvider">
        UPDATE message_provider
        SET message_type = #{messageType},
            provider_key = #{providerKey},
            provider_user_name = #{providerUserName},
            provider_user_pwd = #{providerUserPwd},
            provider_config = #{providerConfig}
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM message_provider WHERE id = #{id}
    </delete>

</mapper>
