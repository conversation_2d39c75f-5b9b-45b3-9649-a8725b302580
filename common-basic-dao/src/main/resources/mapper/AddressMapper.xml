<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.address.dao.mapper.AddressMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cashop.address.dao.entity.Address">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="name_cn" property="nameCn" jdbcType="VARCHAR"/>
        <result column="name_en" property="nameEn" jdbcType="VARCHAR"/>
        <result column="name_local" property="nameLocal" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="TINYINT"/>
        <result column="parent_code" property="parentCode" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="flag_img" property="flagImg" jdbcType="VARCHAR"/>
        <result column="postal_code" property="postalCode" jdbcType="VARCHAR"/>
        <result column="telephone_code" property="telephoneCode" jdbcType="VARCHAR"/>
        <result column="has_child" property="hasChild" jdbcType="TINYINT"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, code, name, name_cn, name_en, name_local, level, parent_code, country_code,
        flag_img, postal_code, telephone_code, has_child, create_by, create_time, update_by, update_time, status
    </sql>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO address (
            code, name, name_cn, name_en, name_local, level, parent_code, country_code,
            flag_img, postal_code, has_child, create_by, create_time, update_by, update_time, status
        ) VALUES
        <foreach collection="addresses" item="item" separator=",">
            (
                #{item.code}, #{item.name}, #{item.nameCn}, #{item.nameEn}, #{item.nameLocal},
                #{item.level}, #{item.parentCode}, #{item.countryCode}, #{item.flagImg},
                #{item.postalCode}, #{item.hasChild}, #{item.createBy}, #{item.createTime},
                #{item.updateBy}, #{item.updateTime}, #{item.status}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE id = #{id} AND status = 1
    </select>

    <!-- 根据编码查询 -->
    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE code = #{code} AND status = 1
    </select>

    <!-- 查询所有国家列表 -->
    <select id="selectAllCountries" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE level = 1 AND status = 1
        ORDER BY name_en
    </select>

    <!-- 根据国家编码查询省级行政区划列表 -->
    <select id="selectProvincesByCountryCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE level = 2 AND country_code = #{countryCode} AND status = 1
        ORDER BY name_en
    </select>

    <!-- 根据省编码查询市级行政区划列表 -->
    <select id="selectCitiesByProvinceCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE level = 3 AND parent_code = #{provinceCode} AND status = 1
        ORDER BY name_en
    </select>

    <!-- 根据级别和父级编码查询 -->
    <select id="selectByLevelAndParentCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE level = #{level} AND status = 1
        <if test="parentCode != null">
            AND parent_code = #{parentCode}
        </if>
        <if test="parentCode == null">
            AND parent_code IS NULL
        </if>
        ORDER BY name_en
    </select>

    <!-- 根据级别查询 -->
    <select id="selectByLevel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM address
        WHERE level = #{level} AND status = 1
        ORDER BY name_en
    </select>

    <!-- 更新地址信息 -->
    <update id="updateById" parameterType="com.cashop.address.dao.entity.Address">
        UPDATE address
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="nameLocal != null">name_local = #{nameLocal},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="flagImg != null">flag_img = #{flagImg},</if>
            <if test="postalCode != null">postal_code = #{postalCode},</if>
            <if test="telephoneCode != null">telephone_code = #{telephoneCode},</if>
            <if test="hasChild != null">has_child = #{hasChild},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        UPDATE address SET status = 0, update_time = NOW() WHERE id = #{id}
    </delete>

    <!-- 统计数量 -->
    <select id="countByLevelAndCountryCode" resultType="long">
        SELECT COUNT(*)
        FROM address
        WHERE level = #{level} AND status = 1
        <if test="countryCode != null">
            AND country_code = #{countryCode}
        </if>
    </select>

</mapper>
