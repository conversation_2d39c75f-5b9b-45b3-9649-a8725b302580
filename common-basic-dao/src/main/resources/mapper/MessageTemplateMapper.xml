<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.message.dao.mapper.MessageTemplateMapper">

    <resultMap id="BaseResultMap" type="com.cashop.message.dao.entity.MessageTemplate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="scene_code" property="sceneCode" jdbcType="VARCHAR"/>
        <result column="language" property="language" jdbcType="VARCHAR"/>
        <result column="message_type" property="messageType" jdbcType="VARCHAR"/>
        <result column="message_title" property="messageTitle" jdbcType="VARCHAR"/>
        <result column="message_content" property="messageContent" jdbcType="LONGVARCHAR"/>
        <result column="message_remark" property="messageRemark" jdbcType="VARCHAR"/>
        <result column="retry_config" property="retryConfig" jdbcType="TINYINT"/>
        <result column="provider_key" property="providerKey" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, scene_code, language, message_type, message_title, message_content, 
        message_remark, retry_config, provider_key, created_time, updated_time
    </sql>

    <insert id="insert" parameterType="com.cashop.message.dao.entity.MessageTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO message_template (
            scene_code, language, message_type, message_title, message_content,
            message_remark, retry_config, provider_key
        ) VALUES (
            #{sceneCode}, #{language}, #{messageType}, #{messageTitle}, #{messageContent},
            #{messageRemark}, #{retryConfig}, #{providerKey}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE id = #{id}
    </select>

    <select id="selectBySceneAndLanguageAndType" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE scene_code = #{sceneCode} 
          AND language = #{language} 
          AND message_type = #{messageType}
    </select>

    <select id="selectBySceneCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE scene_code = #{sceneCode}
        ORDER BY created_time DESC
    </select>

    <select id="selectByMessageType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_template
        WHERE message_type = #{messageType}
        ORDER BY created_time DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM message_template
        ORDER BY created_time DESC
    </select>

    <update id="update" parameterType="com.cashop.message.dao.entity.MessageTemplate">
        UPDATE message_template
        SET scene_code = #{sceneCode},
            language = #{language},
            message_type = #{messageType},
            message_title = #{messageTitle},
            message_content = #{messageContent},
            message_remark = #{messageRemark},
            retry_config = #{retryConfig},
            provider_key = #{providerKey}
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM message_template WHERE id = #{id}
    </delete>

</mapper>
