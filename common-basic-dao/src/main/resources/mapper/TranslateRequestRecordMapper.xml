<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.translate.dao.mapper.TranslateRequestRecordMapper">

    <resultMap id="BaseResultMap" type="com.cashop.translate.dao.entity.TranslateRequestRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="request_type" property="requestType" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="VARCHAR"/>
        <result column="source_language" property="sourceLanguage" jdbcType="VARCHAR"/>
        <result column="target_language" property="targetLanguage" jdbcType="VARCHAR"/>
        <result column="image_count" property="imageCount" jdbcType="INTEGER"/>
        <result column="request_params" property="requestParams" jdbcType="LONGVARCHAR"/>
        <result column="request_content" property="requestContent" jdbcType="LONGVARCHAR"/>
        <result column="ext" property="ext" jdbcType="LONGVARCHAR"/>
        <result column="request_status" property="requestStatus" jdbcType="VARCHAR"/>
        <result column="cloud_api_status" property="cloudApiStatus" jdbcType="VARCHAR"/>
        <result column="cloud_api_response" property="cloudApiResponse" jdbcType="LONGVARCHAR"/>
        <result column="cloud_api_async_status" property="cloudApiAsyncStatus" jdbcType="VARCHAR"/>
        <result column="cloud_api_async_response" property="cloudApiAsyncResponse" jdbcType="LONGVARCHAR"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="max_retry_count" property="maxRetryCount" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="image_sync_result_url" property="imageSyncResultUrl" jdbcType="VARCHAR"/>
        <result column="image_batch_results" property="imageBatchResults" jdbcType="LONGVARCHAR"/>
        <result column="translated_text" property="translatedText" jdbcType="LONGVARCHAR"/>
        <result column="callback" property="callback" jdbcType="VARCHAR"/>
        <result column="callback_status" property="callbackStatus" jdbcType="INTEGER"/>
        <result column="callback_response" property="callbackResponse" jdbcType="LONGVARCHAR"/>
        <result column="callback_retry_count" property="callbackRetryCount" jdbcType="INTEGER"/>
        <result column="callback_max_retry_count" property="callbackMaxRetryCount" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, request_id, request_type, provider, source_language, target_language, image_count,
        request_params, request_content, ext, request_status, cloud_api_status, cloud_api_response, cloud_api_async_status,
        cloud_api_async_response, task_id, retry_count, max_retry_count, error_message,
        image_sync_result_url, image_batch_results, translated_text,
        callback, callback_status, callback_response, callback_retry_count, callback_max_retry_count,
        created_time, updated_time
    </sql>

    <insert id="insert" parameterType="com.cashop.translate.dao.entity.TranslateRequestRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO translate_request_record (
            request_id, request_type, provider, source_language, target_language, image_count,
            request_params, request_content, ext, request_status, cloud_api_status, cloud_api_response, cloud_api_async_status,
            cloud_api_async_response, task_id, retry_count, max_retry_count, error_message,
            callback, callback_status, callback_retry_count, callback_max_retry_count
        ) VALUES (
            #{requestId}, #{requestType}, #{provider}, #{sourceLanguage}, #{targetLanguage}, #{imageCount},
            #{requestParams}, #{requestContent}, #{ext}, #{requestStatus}, #{cloudApiStatus}, #{cloudApiResponse}, #{cloudApiAsyncStatus},
            #{cloudApiAsyncResponse}, #{taskId}, #{retryCount}, #{maxRetryCount}, #{errorMessage},
            #{callback}, #{callbackStatus}, #{callbackRetryCount}, #{callbackMaxRetryCount}
        )
    </insert>

    <select id="selectByRequestId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE request_id = #{requestId}
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE id = #{id}
    </select>

    <update id="updateById" parameterType="com.cashop.translate.dao.entity.TranslateRequestRecord">
        UPDATE translate_request_record
        SET request_type = #{requestType},
            provider = #{provider},
            source_language = #{sourceLanguage},
            target_language = #{targetLanguage},
            image_count = #{imageCount},
            request_params = #{requestParams},
            request_status = #{requestStatus},
            cloud_api_status = #{cloudApiStatus},
            cloud_api_response = #{cloudApiResponse},
            cloud_api_async_status = #{cloudApiAsyncStatus},
            cloud_api_async_response = #{cloudApiAsyncResponse},
            task_id = #{taskId},
            retry_count = #{retryCount},
            max_retry_count = #{maxRetryCount},
            error_message = #{errorMessage}
        WHERE id = #{id}
    </update>

    <update id="updateCloudApiStatus">
        UPDATE translate_request_record
        SET cloud_api_status = #{cloudApiStatus},
            cloud_api_response = #{cloudApiResponse},
            request_status = #{requestStatus},
            task_id = #{taskId},
            error_message = #{errorMessage},
            image_sync_result_url = #{imageSyncResultUrl},
            translated_text = #{translatedText}
        WHERE id = #{id}
    </update>

    <update id="updateAsyncStatus">
        UPDATE translate_request_record
        SET cloud_api_async_status = #{cloudApiAsyncStatus},
            cloud_api_async_response = #{cloudApiAsyncResponse},
            request_status = #{requestStatus},
            retry_count = #{retryCount},
            error_message = #{errorMessage},
            image_batch_results = #{imageBatchResults}
        WHERE id = #{id}
    </update>

    <select id="selectPendingAsyncRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE created_time &gt; DATE_SUB(NOW(), INTERVAL 1 DAY)
          AND request_type = #{requestType}
          AND cloud_api_status = #{cloudApiStatus}
          AND (cloud_api_async_status IS NULL OR cloud_api_async_status = 'PROCESSING')
          AND retry_count &lt; #{maxRetryCount}
          AND task_id IS NOT NULL
          AND provider IN
          <foreach item="item" index="index" collection="providerList" open="(" separator="," close=")">
              #{item}
          </foreach>
        ORDER BY created_time ASC
    </select>

    <select id="selectTimeoutRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE created_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutHours} HOUR)
          AND request_status in ('PENDING', 'PROCESSING')
        ORDER BY created_time ASC
    </select>

    <delete id="deleteById">
        DELETE FROM translate_request_record WHERE id = #{id}
    </delete>

    <select id="selectPendingCallbackRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE created_time &gt; DATE_SUB(NOW(), INTERVAL 1 DAY)
          AND callback_status IN (0, 2)
          AND request_status = 'SUCCESS'
          AND cloud_api_async_status = 'SUCCESS'
          AND callback IS NOT NULL
          AND callback != ''
          AND (callback_retry_count IS NULL OR callback_retry_count &lt; callback_max_retry_count)
        ORDER BY created_time ASC
    </select>

    <update id="updateCallbackStatus">
        UPDATE translate_request_record
        SET callback_status = #{callbackStatus},
            callback_response = #{callbackResponse},
            callback_retry_count = #{callbackRetryCount}
        WHERE id = #{id}
    </update>

    <select id="selectPendingAsyncTextBatchTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE created_time &gt; DATE_SUB(NOW(), INTERVAL 1 DAY)
          AND request_type = 'ASYNC_TEXT_BATCH'
          AND request_status = 'PENDING'
        ORDER BY created_time ASC
        LIMIT 100
    </select>

    <select id="selectPendingAsyncTextBatchCallbackTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM translate_request_record
        WHERE created_time &gt; DATE_SUB(NOW(), INTERVAL 1 DAY)
          AND request_type = 'ASYNC_TEXT_BATCH'
          AND request_status = 'SUCCESS'
          AND callback_status IN (0, 2)
          AND callback IS NOT NULL
          AND callback != ''
          AND (callback_retry_count IS NULL OR callback_retry_count &lt; callback_max_retry_count)
        ORDER BY created_time ASC
        LIMIT 100
    </select>

</mapper>
