#!/bin/bash

# 消息发送API测试脚本
# 使用方法: ./test-message-api.sh [base_url]
# 默认base_url为 http://localhost:8080

BASE_URL=${1:-"http://localhost:8080"}
API_BASE="$BASE_URL/api/message"

echo "=== 消息发送API测试 ==="
echo "Base URL: $BASE_URL"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    
    echo -e "${YELLOW}测试: $name${NC}"
    echo "URL: $method $url"
    
    if [ -n "$data" ]; then
        echo "Data: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "Status: $status"
    echo "Response: $body"
    
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败 (期望状态码: $expected_status, 实际: $status)${NC}"
    fi
    echo "----------------------------------------"
}

# 1. 测试邮件发送 - 成功案例
echo "1. 测试邮件发送 - 用户注册验证码"
test_api "邮件发送 - 用户注册" "POST" "$API_BASE/email/send" '{
    "appId": "test_app",
    "sceneCode": "user_register",
    "language": "en",
    "toEmailList": ["<EMAIL>"],
    "templateParams": {
        "userName": "张三",
        "verificationCode": "123456",
        "appName": "测试应用",
        "expireMinutes": "10"
    },
    "emailHost": "smtp.exmail.qq.com",
    "emailPort": 465,
    "fromEmail": "<EMAIL>",
    "fromEmailPwd": "Okmijn456123"
}' "200"

# 2. 测试邮件发送 - 中文模版
echo "2. 测试邮件发送 - 中文模版"
test_api "邮件发送 - 中文模版" "POST" "$API_BASE/email/send" '{
    "appId": "test_app",
    "sceneCode": "user_register",
    "language": "zh",
    "toEmailList": ["<EMAIL>"],
    "templateParams": {
        "userName": "张三",
        "verificationCode": "654321",
        "appName": "测试应用",
        "expireMinutes": "10"
    },
    "emailHost": "smtp.exmail.qq.com",
    "emailPort": 465,
    "fromEmail": "<EMAIL>",
    "fromEmailPwd": "Okmijn456123"
}' "200"

# 3. 测试邮件发送 - 密码重置
echo "3. 测试邮件发送 - 密码重置"
test_api "邮件发送 - 密码重置" "POST" "$API_BASE/email/send" '{
    "appId": "test_app",
    "sceneCode": "password_reset",
    "language": "en",
    "toEmailList": ["<EMAIL>"],
    "templateParams": {
        "userName": "John Doe",
        "resetCode": "RESET123",
        "appName": "Test App",
        "expireMinutes": "30"
    },
    "emailHost": "smtp.exmail.qq.com",
    "emailPort": 465,
    "fromEmail": "<EMAIL>",
    "fromEmailPwd": "Okmijn456123"
}' "200"

# 4. 测试邮件发送 - 自定义标题
echo "4. 测试邮件发送 - 自定义标题"
test_api "邮件发送 - 自定义标题" "POST" "$API_BASE/email/send" '{
    "appId": "test_app",
    "sceneCode": "system_notification",
    "language": "zh",
    "toEmailList": ["<EMAIL>"],
    "emailTitle": "重要系统通知",
    "templateParams": {
        "userName": "用户",
        "notificationContent": "您的账户安全设置已更新",
        "notificationTime": "2024-01-15 10:30:00",
        "appName": "测试系统"
    },
    "emailHost": "smtp.exmail.qq.com",
    "emailPort": 465,
    "fromEmail": "<EMAIL>",
    "fromEmailPwd": "Okmijn456123"
}' "200"

# 5. 测试邮件发送 - 参数验证失败
echo "5. 测试邮件发送 - 参数验证失败"
test_api "邮件发送 - 缺少必填参数" "POST" "$API_BASE/email/send" '{
    "sceneCode": "user_register"
}' "400"

# 6. 测试邮件发送 - 模版不存在
echo "6. 测试邮件发送 - 模版不存在"
test_api "邮件发送 - 模版不存在" "POST" "$API_BASE/email/send" '{
    "appId": "test_app",
    "sceneCode": "nonexistent_scene",
    "language": "en",
    "toEmailList": ["<EMAIL>"],
    "emailHost": "smtp.exmail.qq.com",
    "emailPort": 465,
    "fromEmail": "<EMAIL>",
    "fromEmailPwd": "Okmijn456123"
}' "200"

# 7. 测试查询邮件发送状态 - 不存在的请求ID
echo "7. 测试查询邮件发送状态"
test_api "查询邮件状态 - 不存在的ID" "GET" "$API_BASE/email/status/nonexistent_request_id" "" "200"

# 8. 测试重试邮件发送 - 不存在的请求ID
echo "8. 测试重试邮件发送"
test_api "重试邮件发送 - 不存在的ID" "POST" "$API_BASE/email/retry/nonexistent_request_id" "" "200"

echo
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo "请检查上述测试结果，确保所有API都能正常响应。"
echo "注意：实际的邮件发送可能会失败，这取决于SMTP服务器配置和网络连接。"
