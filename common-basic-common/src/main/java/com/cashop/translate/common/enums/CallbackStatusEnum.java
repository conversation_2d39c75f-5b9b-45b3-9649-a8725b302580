package com.cashop.translate.common.enums;

/**
 * 回调状态枚举
 * 
 * <AUTHOR>
 */
public enum CallbackStatusEnum {

    /**
     * 初始状态
     */
    INITIAL(0, "初始"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAILED(2, "失败");

    private final Integer code;
    private final String description;

    CallbackStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static CallbackStatusEnum fromCode(Integer code) {
        for (CallbackStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown callback status code: " + code);
    }
}
