package com.cashop.translate.common.enums;

/**
 * 请求类型枚举
 * 
 * <AUTHOR>
 */
public enum RequestTypeEnum {

    /**
     * 同步单张图片翻译
     */
    SYNC_IMAGE_SINGLE("SYNC_IMAGE_SINGLE", "同步单张图片翻译"),

    /**
     * 异步批量图片翻译
     */
    ASYNC_IMAGE_BATCH("ASYNC_IMAGE_BATCH", "异步批量图片翻译"),

    /**
     * 同步单条文本翻译
     */
    SYNC_TEXT_SINGLE("SYNC_TEXT_SINGLE", "同步单条文本翻译"),

    /**
     * 异步批量文本翻译
     */
    ASYNC_TEXT_BATCH("ASYNC_TEXT_BATCH", "异步批量文本翻译");

    private final String code;
    private final String description;

    RequestTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RequestTypeEnum fromCode(String code) {
        for (RequestTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown request type code: " + code);
    }
}
