package com.cashop.language.web.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cashop.common.base.LanguageEnum;
import com.cashop.common.base.response.Result;
import com.cashop.language.facade.LanguageVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/base/common-basic/open/language/api")//外网可见不鉴权：/域/服务/open
@Tag(name = "语言管理", description = "语言管理相关接口")
public class LanguageController {

    @Operation(summary = "查询语言列表", description = "获取所有可用的语言列表")
    @GetMapping("/languages")
    public Result<List<LanguageVO>> getAllLanguages() {
        
        List<LanguageVO> languageList = Arrays.asList(LanguageEnum.values()).stream().map(languageEnum -> {
            LanguageVO languageVO = new LanguageVO();
            languageVO.setName(languageEnum.name());
            languageVO.setCode(languageEnum.getValue());
            return languageVO;
        }).collect(Collectors.toList());
        return Result.success(languageList);
    }
    


}
