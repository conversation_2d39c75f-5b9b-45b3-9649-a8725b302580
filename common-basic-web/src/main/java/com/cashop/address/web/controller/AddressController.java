package com.cashop.address.web.controller;

import com.cashop.address.dao.entity.Address;
import com.cashop.address.facade.dto.AddressGroup;
import com.cashop.address.facade.dto.AddressGroupVO;
import com.cashop.address.facade.dto.AddressVO;
import com.cashop.address.service.AddressService;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 地址管理控制器
 *
 * <AUTHOR>
 */
@RestController
//@RequestMapping("/api/address")
@RequestMapping("/base/common-basic/open/address/api")//外网可见不鉴权：/域/服务/open
@Tag(name = "地址管理", description = "地址管理相关接口，包括国家、省、市三级地址查询")
public class AddressController {

    private static final Logger logger = LoggerFactory.getLogger(AddressController.class);

    @Autowired
    private AddressService addressService;

    @Operation(summary = "初始化地址数据", description = "从JSON文件中读取地址数据并导入数据库（注意：需要先手动创建address表）")
    @GetMapping("/init")
    public Result<String> initAddressData() {
        try {
            String message = addressService.initAddressData();
            return Result.success(message);
        } catch (Exception e) {
            logger.error("初始化地址数据失败", e);
            return Result.error("初始化失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询所有国家列表", description = "获取所有可用的国家列表")
    @GetMapping("/countries")
    public Result<List<Address>> getAllCountries(@RequestParam(name="language", defaultValue = "en" ) String language) {
        try {
            List<Address> countries = addressService.getAllCountries();
            return Result.success(countries);
        } catch (Exception e) {
            logger.error("查询国家列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据国家编码查询省级行政区划列表", description = "根据指定的国家编码查询该国家下的所有省/州")
    @GetMapping("/provinces")
    public Result<List<Address>> getProvincesByCountryCode(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode) {
        try {
            List<Address> provinces = addressService.getProvincesByCountryCode(countryCode);
            return Result.success(provinces);
        } catch (Exception e) {
            logger.error("查询省级行政区划失败，国家编码: {}", countryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据省编码查询市级行政区划列表", description = "根据指定的省编码查询该省下的所有市/区")
    @GetMapping("/cities")
    public Result<List<Address>> getCitiesByProvinceCode(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "省编码", required = true, example = "3907")
            @RequestParam("provinceCode") String provinceCode) {
        try {
            List<Address> cities = addressService.getCitiesByProvinceCode(provinceCode);
            return Result.success(cities);
        } catch (Exception e) {
            logger.error("查询市级行政区划失败，省编码: {}", provinceCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据国家编码查询省市级列表", description = "根据国家编码查询省市级列表")
    @GetMapping("/l1l2")
    public Result<List<AddressVO>> getCitiesProvinceByCountryCode(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode) {
        List<AddressVO> addressVOList = new ArrayList<>();
        try {
            List<Address> addresses = addressService.getProvincesByCountryCode(countryCode);
            for (Address address : addresses) {
                AddressVO provinceAddressVO = addressTransformToAddressVO(address);
                //查询下级城市
                List<Address> cities = addressService.getCitiesByProvinceCode(address.getCode());
                provinceAddressVO.setChildrens(addressListTransformToAddressVOList(cities));
                addressVOList.add(provinceAddressVO);
            }
            return Result.success(addressVOList);
        } catch (Exception e) {
            logger.error("查询省市级列表失败，国家编码: {}", countryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }


    @Operation(summary = "查询所有国家列表", description = "获取所有可用的国家列表")
    @GetMapping("/countries/group")
    public Result<List<AddressGroup>> getAllCountriesResutlGrouop(@RequestParam(name="language", defaultValue = "en" ) String language) {
        try {
            List<Address> countries = addressService.getAllCountries();
            List<AddressGroupVO> addressVOList = addressListTransformToAddressGroupVOList(countries);
            List<AddressGroup> addressGroupList = toAddressGroupList(addressVOList);
            return Result.success(addressGroupList);
        } catch (Exception e) {
            logger.error("查询国家列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据国家编码查询省级行政区划列表", description = "根据指定的国家编码查询该国家下的所有省/州")
    @GetMapping("/provinces/group")
    public Result<List<AddressGroup>> getProvincesByCountryCodeResutlGrouo(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode) {
        try {
            List<Address> provinces = addressService.getProvincesByCountryCode(countryCode);
            List<AddressGroupVO> addressVOList = addressListTransformToAddressGroupVOList(provinces);
            List<AddressGroup> addressGroupList = toAddressGroupList(addressVOList);
            return Result.success(addressGroupList);
        } catch (Exception e) {
            logger.error("查询省级行政区划失败，国家编码: {}", countryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据省编码查询市级行政区划列表", description = "根据指定的省编码查询该省下的所有市/区")
    @GetMapping("/cities/group")
    public Result<List<AddressGroup>> getCitiesByProvinceCodeResutlGrouo(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "省编码", required = true, example = "3907")
            @RequestParam("provinceCode") String provinceCode) {
        try {
            List<Address> cities = addressService.getCitiesByProvinceCode(provinceCode);
            List<AddressGroupVO> addressVOList = addressListTransformToAddressGroupVOList(cities);
            List<AddressGroup> addressGroupList = toAddressGroupList(addressVOList);
            return Result.success(addressGroupList);
        } catch (Exception e) {
            logger.error("查询市级行政区划失败，省编码: {}", provinceCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据国家编码查询省市级列表", description = "根据国家编码查询省市级列表")
    @GetMapping("/l1l2/group")
    public Result<List<AddressGroup>> getCitiesProvinceByCountryCodeResutlGrouo(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode) {
        List<AddressGroupVO> addressVOList = new ArrayList<>();
        try {
            List<Address> addresses = addressService.getProvincesByCountryCode(countryCode);
            for (Address address : addresses) {
                AddressGroupVO provinceAddressVO = addressTransformToAddressGroupVO(address);
                //查询下级城市
                List<Address> cities = addressService.getCitiesByProvinceCode(address.getCode());
                provinceAddressVO.setChildrens(toAddressGroupList(addressListTransformToAddressGroupVOList(cities)));
                addressVOList.add(provinceAddressVO);
            }
            List<AddressGroup> addressGroupList = toAddressGroupList(addressVOList);
            return Result.success(addressGroupList);
        } catch (Exception e) {
            logger.error("查询省市级列表失败，国家编码: {}", countryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }


    private List<AddressGroup> toAddressGroupList(List<AddressGroupVO> addressVOList) {
        //将addressVOList按groupKey的首字母分组，分组title为groupKey的首字母，分组的data为addressVOList中groupKey相同的元素
        List<AddressGroup> addressGroupList = new ArrayList<>();
        addressVOList.stream().collect(Collectors.groupingBy(AddressGroupVO::getGroupKey)).forEach((k, v) -> {
            AddressGroup addressGroup = new AddressGroup();
            addressGroup.setTitle(k);
            addressGroup.setData(v);
            addressGroupList.add(addressGroup);
        });
        addressGroupList.sort((o1, o2) -> o1.getTitle().compareTo(o2.getTitle()));
        return addressGroupList;
    }

    private List<AddressVO> addressListTransformToAddressVOList(List<Address> addressList) {
        List<AddressVO> addressVOList = new ArrayList<>();
        for (Address address : addressList) {
            addressVOList.add(addressTransformToAddressVO(address));
        }
        return addressVOList;
    }

    private List<AddressGroupVO> addressListTransformToAddressGroupVOList(List<Address> addressList) {
        List<AddressGroupVO> addressVOList = new ArrayList<>();
        for (Address address : addressList) {
            addressVOList.add(addressTransformToAddressGroupVO(address));
        }
        return addressVOList;
    }

    private AddressVO addressTransformToAddressVO(Address address) {
        AddressVO addressVO = new AddressVO();
        //Address transform to AddressVO
        addressVO.setId(address.getId());
        addressVO.setCode(address.getCode());
        addressVO.setName(address.getName());
        addressVO.setNameCn(address.getNameCn());
        addressVO.setNameEn(address.getNameEn());
        addressVO.setGroupKey(null != address.getNameEn() ? address.getNameEn().substring(0, 1).toUpperCase() : "");
        addressVO.setNameLocal(address.getNameLocal());
        addressVO.setLevel(address.getLevel());
        addressVO.setParentCode(address.getParentCode());
        addressVO.setCountryCode(address.getCountryCode());
        addressVO.setFlagImg(address.getFlagImg());
        addressVO.setPostalCode(address.getPostalCode());
        addressVO.setTelephoneCode(address.getTelephoneCode());
        addressVO.setHasChild(address.getHasChild());
        return addressVO;
    }

    private AddressGroupVO addressTransformToAddressGroupVO(Address address) {
        AddressGroupVO addressVO = new AddressGroupVO();
        //Address transform to AddressVO
        addressVO.setId(address.getId());
        addressVO.setCode(address.getCode());
        addressVO.setName(address.getName());
        addressVO.setNameCn(address.getNameCn());
        addressVO.setNameEn(address.getNameEn());
        addressVO.setGroupKey(null != address.getNameEn() ? address.getNameEn().substring(0, 1).toUpperCase() : "");
        addressVO.setNameLocal(address.getNameLocal());
        addressVO.setLevel(address.getLevel());
        addressVO.setParentCode(address.getParentCode());
        addressVO.setCountryCode(address.getCountryCode());
        addressVO.setFlagImg(address.getFlagImg());
        addressVO.setPostalCode(address.getPostalCode());
        addressVO.setTelephoneCode(address.getTelephoneCode());
        addressVO.setHasChild(address.getHasChild());
        return addressVO;
    }

    @Operation(summary = "根据编码查询地址信息", description = "根据地址编码查询具体的地址信息")
    @GetMapping("/detail")
    public Result<Address> getAddressByCode(
            @Parameter(description = "地址编码", required = true, example = "US")
            @RequestParam("code") String code) {
        try {
            Address address = addressService.getAddressByCode(code);
            if (address != null) {
                return Result.success(address);
            } else {
                return Result.error("未找到对应的地址信息");
            }
        } catch (Exception e) {
            logger.error("查询地址详情失败，编码: {}", code, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "统计地址数量", description = "统计指定级别和国家的地址数量")
    @GetMapping("/count")
    public Result<Long> countAddresses(
            @Parameter(description = "地址级别：1-国家，2-省，3-市", required = true, example = "1")
            @RequestParam("level") Integer level,
            @Parameter(description = "国家编码（可选）", example = "US")
            @RequestParam(value = "countryCode", required = false) String countryCode) {
        try {
            long count = addressService.countAddresses(level, countryCode);
            return Result.success(count);
        } catch (Exception e) {
            logger.error("统计地址数量失败，级别: {}, 国家编码: {}", level, countryCode, e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }
}
