package com.cashop.message.web.controller;

import com.cashop.common.base.response.Result;
import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.facade.dto.EmailSendResponse;
import com.cashop.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * 消息发送控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/message")
@Tag(name = "消息发送服务", description = "消息发送相关接口，支持邮件、短信、推送等")
@Validated
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);

    @Autowired
    private MessageService messageService;

    /**
     * 发送邮件
     * 
     * @param request 邮件发送请求
     * @return 邮件发送响应
     */
    @Operation(summary = "发送邮件", description = "根据场景编码和模版参数发送邮件")
    @PostMapping("/email/send")
    public CompletableFuture<Result<EmailSendResponse>> sendEmail(@Valid @RequestBody EmailSendRequest request) {
        logger.info("收到邮件发送请求，appId: {}, sceneCode: {}, toEmailList: {}", 
                   request.getAppId(), request.getSceneCode(), request.getToEmailList());

        return messageService.sendEmail(request)
                .thenApply(response -> {
                    if (response.getSuccess()) {
                        logger.info("邮件发送成功，requestId: {}", response.getRequestId());
                        return Result.<EmailSendResponse>success(response, "邮件发送成功");
                    } else {
                        logger.error("邮件发送失败，requestId: {}, error: {}",
                                   response.getRequestId(), response.getErrorMessage());
                        return Result.<EmailSendResponse>error("500", response.getErrorMessage());
                    }
                })
                .exceptionally(throwable -> {
                    logger.error("邮件发送异常", throwable);
                    return Result.<EmailSendResponse>error("500", "邮件发送异常: " + throwable.getMessage());
                });
    }

    /**
     * 重试邮件发送
     * 
     * @param requestId 请求ID
     * @return 邮件发送响应
     */
    @Operation(summary = "重试邮件发送", description = "重试失败的邮件发送")
    @PostMapping("/email/retry/{requestId}")
    public CompletableFuture<Result<EmailSendResponse>> retryEmailSend(
            @Parameter(description = "请求ID", example = "req_123456")
            @PathVariable @NotBlank(message = "请求ID不能为空") String requestId) {
        
        logger.info("收到邮件重试发送请求，requestId: {}", requestId);

        return messageService.retryEmailSend(requestId)
                .thenApply(response -> {
                    if (response.getSuccess()) {
                        logger.info("邮件重试发送成功，requestId: {}", response.getRequestId());
                        return Result.<EmailSendResponse>success(response, "邮件重试发送成功");
                    } else {
                        logger.error("邮件重试发送失败，requestId: {}, error: {}",
                                   response.getRequestId(), response.getErrorMessage());
                        return Result.<EmailSendResponse>error("500", response.getErrorMessage());
                    }
                })
                .exceptionally(throwable -> {
                    logger.error("邮件重试发送异常", throwable);
                    return Result.<EmailSendResponse>error("500", "邮件重试发送异常: " + throwable.getMessage());
                });
    }

    /**
     * 查询邮件发送状态
     * 
     * @param requestId 请求ID
     * @return 邮件发送响应
     */
    @Operation(summary = "查询邮件发送状态", description = "根据请求ID查询邮件发送状态")
    @GetMapping("/email/status/{requestId}")
    public Result<EmailSendResponse> getEmailSendStatus(
            @Parameter(description = "请求ID", example = "req_123456")
            @PathVariable @NotBlank(message = "请求ID不能为空") String requestId) {
        
        logger.info("收到邮件发送状态查询请求，requestId: {}", requestId);

        try {
            EmailSendResponse response = messageService.getEmailSendStatus(requestId);
            if (response.getSuccess() != null) {
                logger.info("邮件发送状态查询成功，requestId: {}, status: {}", 
                           requestId, response.getSendStatus());
                return Result.success(response, "查询成功");
            } else {
                logger.error("邮件发送状态查询失败，requestId: {}, error: {}", 
                           requestId, response.getErrorMessage());
                return Result.error("500", response.getErrorMessage());
            }
        } catch (Exception e) {
            logger.error("邮件发送状态查询异常，requestId: {}", requestId, e);
            return Result.error("查询异常: " + e.getMessage());
        }
    }
}
