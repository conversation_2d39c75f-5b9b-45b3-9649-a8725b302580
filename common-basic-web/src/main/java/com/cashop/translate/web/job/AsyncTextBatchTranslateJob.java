package com.cashop.translate.web.job;

import com.cashop.translate.service.job.AsyncTextBatchTranslateJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 异步批量文本翻译定时任务
 * 
 * <AUTHOR>
 */
@Component
public class AsyncTextBatchTranslateJob {

    private static final Logger logger = LoggerFactory.getLogger(AsyncTextBatchTranslateJob.class);

    @Autowired
    private AsyncTextBatchTranslateJobService asyncTextBatchTranslateJobService;

    /**
     * 处理异步批量文本翻译任务
     */
    @XxlJob("asyncTextBatchTranslateJobHandler")
    public void asyncTextBatchTranslateJobHandler() {
        XxlJobHelper.log("开始执行异步批量文本翻译任务");
        logger.info("开始执行异步批量文本翻译任务");

        try {
            asyncTextBatchTranslateJobService.processAsyncTextBatchTranslateTasks();
            
            XxlJobHelper.log("异步批量文本翻译任务执行完成");
            logger.info("异步批量文本翻译任务执行完成");
            
        } catch (Exception e) {
            String errorMsg = "异步批量文本翻译任务执行异常: " + e.getMessage();
            XxlJobHelper.log(errorMsg);
            logger.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
        }
    }
}
