package com.cashop.translate.web.controller;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.common.base.response.Result;
import com.cashop.translate.client.aliyun.AliyunTranslateClient;
import com.cashop.translate.service.job.BatchTranslateJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 测试控制器 - 仅在本地环境启用
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/test")
@Tag(name = "测试接口", description = "用于测试阿里云翻译服务的接口")
@ConditionalOnProperty(name = "spring.profiles.active", havingValue = "local")
public class TestController {

    private static final Logger logger = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private AliyunTranslateClient aliyunTranslateClient;

    @Autowired
    private BatchTranslateJobService batchTranslateJobService;

    @Operation(summary = "测试阿里云同步翻译", description = "使用测试图片URL测试阿里云同步翻译功能")
    @GetMapping("/aliyun/sync")
    public CompletableFuture<? extends Result<?>> testAliyunSync(
            @Parameter(description = "图片URL", example = "https://example.com/image.jpg")
            @RequestParam(value = "imageUrl", required = false) String imageUrl,
            @Parameter(description = "目标语言", example = "zh")
            @RequestParam(value = "targetLanguage", required = false, defaultValue = "zh") String targetLanguage,
            @Parameter(description = "源语言", example = "en")
            @RequestParam(value = "sourceLanguage", required = false, defaultValue = "en") String sourceLanguage) {
        
        logger.info("开始测试阿里云同步翻译");

        try {
            // 准备测试请求
            TranslateRequest request = new TranslateRequest();
            request.setRequestId("test-sync-" + UUID.randomUUID().toString().substring(0, 8));
            request.setTargetLanguage(targetLanguage);
            request.setSourceLanguage(sourceLanguage);
            
            // 使用提供的URL或默认测试URL
            String testImageUrl = imageUrl != null ? imageUrl : 
                    "https://img.alicdn.com/imgextra/i1/6000000006025/O1CN01Q8Q8Q81rjQqQqQqQq_!!6000000006025-0-tps-200-200.jpg";
            request.setImageUrls(Arrays.asList(testImageUrl));

            logger.info("测试参数: requestId={}, imageUrl={}", request.getRequestId(), testImageUrl);

            // 调用阿里云服务
            return aliyunTranslateClient.translateImageSync(request)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            logger.info("阿里云同步翻译测试成功: {}", response.getRequestId());
                            return Result.success(response, "阿里云同步翻译测试成功"    );
                        } else {
                            logger.error("阿里云同步翻译测试失败: {}", response.getErrorMessage());
                            return Result.error("500", "翻译失败: " + response.getErrorMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云同步翻译测试异常", throwable);
                        return Result.error("测试异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            logger.error("测试请求处理异常", e);
            return CompletableFuture.completedFuture(Result.error("请求处理异常: " + e.getMessage()));
        }
    }

    @Operation(summary = "测试阿里云批量翻译", description = "使用测试图片URL测试阿里云批量翻译功能")
    @GetMapping("/aliyun/batch")
    public CompletableFuture<? extends Result<?>> testAliyunBatch() {
        
        logger.info("开始测试阿里云批量翻译");

        try {
            // 准备测试请求
            TranslateRequest request = new TranslateRequest();
            request.setRequestId("test-batch-" + UUID.randomUUID().toString().substring(0, 8));
            request.setTargetLanguage("zh");
            request.setSourceLanguage("en");
            
            // 使用多个测试图片URL
            request.setImageUrls(Arrays.asList(
                    "https://img.alicdn.com/imgextra/i1/6000000006025/O1CN01Q8Q8Q81rjQqQqQqQq_!!6000000006025-0-tps-200-200.jpg",
                    "https://img.alicdn.com/imgextra/i2/6000000006025/O1CN01Q8Q8Q81rjQqQqQqQq_!!6000000006025-0-tps-200-200.jpg"
            ));

            logger.info("测试参数: requestId={}, imageCount={}", request.getRequestId(), request.getImageUrls().size());

            // 调用阿里云服务
            return aliyunTranslateClient.translateImageBatch(request)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            logger.info("阿里云批量翻译测试成功: requestId={}, taskId={}", 
                                    response.getRequestId(), response.getTaskId());
                            return Result.success(response, "阿里云批量翻译测试成功");
                        } else {
                            logger.error("阿里云批量翻译测试失败: {}", response.getErrorMessage());
                            return Result.error("500", "翻译失败: " + response.getErrorMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云批量翻译测试异常", throwable);
                        return Result.error("测试异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            logger.error("测试请求处理异常", e);
            return CompletableFuture.completedFuture(Result.error("请求处理异常: " + e.getMessage()));
        }
    }

    @Operation(summary = "测试获取批量翻译结果", description = "根据任务ID获取批量翻译结果")
    @GetMapping("/aliyun/batch/result")
    public CompletableFuture<? extends Result<?>> testGetBatchResult(
            @Parameter(description = "任务ID", required = true)
            @RequestParam String taskId) {
        
        logger.info("开始测试获取批量翻译结果，taskId: {}", taskId);

        try {
            // 调用阿里云服务
            return aliyunTranslateClient.getBatchTranslateResult(taskId)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            logger.info("获取批量翻译结果成功: taskId={}, status={}", 
                                    taskId, response.getCloudApiAsyncStatus());
                            return Result.success(response, "获取批量翻译结果成功");
                        } else {
                            logger.error("获取批量翻译结果失败: {}", response.getErrorMessage());
                            return Result.error("500", "获取结果失败: " + response.getErrorMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        logger.error("获取批量翻译结果异常", throwable);
                        return Result.error("获取结果异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            logger.error("测试请求处理异常", e);
            return CompletableFuture.completedFuture(Result.error("请求处理异常: " + e.getMessage()));
        }
    }

    @Operation(summary = "检查阿里云服务可用性", description = "检查阿里云翻译服务是否可用")
    @GetMapping("/aliyun/health")
    public Result<String> testAliyunHealth() {
        
        logger.info("开始检查阿里云服务可用性");

        try {
            boolean available = aliyunTranslateClient.isAvailable();
            String message = available ? "阿里云翻译服务可用" : "阿里云翻译服务不可用";
            
            logger.info("阿里云服务可用性检查结果: {}", available);
            
            if (available) {
                return Result.success(message, "OK");
            } else {
                return Result.error("503", message);
            }

        } catch (Exception e) {
            logger.error("检查阿里云服务可用性异常", e);
            return Result.error("检查服务可用性异常: " + e.getMessage());
        }
    }

    @Operation(summary = "测试阿里云文本翻译", description = "使用测试文本测试阿里云文本翻译功能")
    @GetMapping("/aliyun/text")
    public CompletableFuture<? extends Result<?>> testAliyunTextTranslate(
            @Parameter(description = "待翻译文本", example = "Hello, world!")
            @RequestParam(value = "text", required = false, defaultValue = "Hello, world!") String text,
            @Parameter(description = "目标语言", example = "zh")
            @RequestParam(value = "targetLanguage", required = false, defaultValue = "zh") String targetLanguage,
            @Parameter(description = "源语言", example = "en")
            @RequestParam(value = "sourceLanguage", required = false, defaultValue = "en") String sourceLanguage) {

        logger.info("开始测试阿里云文本翻译");

        try {
            // 准备测试请求
            TranslateRequest request = new TranslateRequest();
            request.setRequestId("test-text-" + UUID.randomUUID().toString().substring(0, 8));
            request.setTargetLanguage(targetLanguage);
            request.setSourceLanguage(sourceLanguage);
            request.setText(text);

            logger.info("测试参数: requestId={}, text={}, sourceLanguage={}, targetLanguage={}",
                    request.getRequestId(), text, sourceLanguage, targetLanguage);

            // 调用阿里云服务
            return aliyunTranslateClient.translateTextSync(request)
                    .thenApply(response -> {
                        if (response.isSuccess()) {
                            logger.info("阿里云文本翻译测试成功: {}", response.getRequestId());
                            return Result.success( response, "阿里云文本翻译测试成功");
                        } else {
                            logger.error("阿里云文本翻译测试失败: {}", response.getErrorMessage());
                            return Result.error("500", "翻译失败: " + response.getErrorMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        logger.error("阿里云文本翻译测试异常", throwable);
                        return Result.error("测试异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            logger.error("测试请求处理异常", e);
            return CompletableFuture.completedFuture(Result.error("请求处理异常: " + e.getMessage()));
        }
    }

    @Operation(summary = "调试批量翻译记录", description = "调试特定requestId的批量翻译记录状态")
    @GetMapping("/debug/batch-record")
    public Result<String> debugBatchRecord(
            @Parameter(description = "请求ID", example = "5dc72719-0ce0-4a8d-bf70-25a47aa0a03f")
            @RequestParam("requestId") String requestId) {

        logger.info("开始调试批量翻译记录，requestId: {}", requestId);

        try {
            batchTranslateJobService.debugRequestRecord(requestId);
            return Result.success("调试完成，请查看日志");
        } catch (Exception e) {
            logger.error("调试批量翻译记录异常", e);
            return Result.error("调试异常: " + e.getMessage());
        }
    }

    @Operation(summary = "手动执行批量翻译结果获取任务", description = "手动触发批量翻译结果获取任务")
    @PostMapping("/debug/batch-job")
    public Result<String> runBatchJob() {

        logger.info("手动执行批量翻译结果获取任务");

        try {
            batchTranslateJobService.batchTranslateResultJob();
            return Result.success("任务执行完成，请查看日志");
        } catch (Exception e) {
            logger.error("手动执行批量翻译结果获取任务异常", e);
            return Result.error("任务执行异常: " + e.getMessage());
        }
    }

    @Operation(summary = "修复批量翻译记录状态", description = "修复特定requestId的批量翻译记录状态，使其能被定时任务扫描")
    @PostMapping("/debug/fix-batch-record")
    public Result<String> fixBatchRecord(
            @Parameter(description = "请求ID", example = "2cdad4cf-ddfa-4cab-b9c2-1dfabb663be2")
            @RequestParam("requestId") String requestId) {

        logger.info("开始修复批量翻译记录状态，requestId: {}", requestId);

        try {
            batchTranslateJobService.fixBatchRecord(requestId);
            return Result.success("记录状态修复完成，请查看日志");
        } catch (Exception e) {
            logger.error("修复批量翻译记录状态异常", e);
            return Result.error("修复异常: " + e.getMessage());
        }
    }
}
