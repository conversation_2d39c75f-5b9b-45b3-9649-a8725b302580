package com.cashop.translate.web.job;

import com.cashop.translate.service.job.AsyncTextBatchCallbackJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 异步批量文本翻译回调定时任务
 * 
 * <AUTHOR>
 */
@Component
public class AsyncTextBatchCallbackJob {

    private static final Logger logger = LoggerFactory.getLogger(AsyncTextBatchCallbackJob.class);

    @Autowired
    private AsyncTextBatchCallbackJobService asyncTextBatchCallbackJobService;

    /**
     * 处理异步批量文本翻译回调任务
     */
    @XxlJob("asyncTextBatchCallbackJobHandler")
    public void asyncTextBatchCallbackJobHandler() {
        XxlJobHelper.log("开始执行异步批量文本翻译回调任务");
        logger.info("开始执行异步批量文本翻译回调任务");

        try {
            asyncTextBatchCallbackJobService.processAsyncTextBatchCallbackTasks();
            
            XxlJobHelper.log("异步批量文本翻译回调任务执行完成");
            logger.info("异步批量文本翻译回调任务执行完成");
            
        } catch (Exception e) {
            String errorMsg = "异步批量文本翻译回调任务执行异常: " + e.getMessage();
            XxlJobHelper.log(errorMsg);
            logger.error(errorMsg, e);
            XxlJobHelper.handleFail(errorMsg);
        }
    }
}
