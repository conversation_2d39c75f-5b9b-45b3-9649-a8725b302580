package com.cashop.translate.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Translation Service Application
 * 
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {
    "com.cashop.translate", 
    "com.cashop.message", 
    "com.cashop.address", 
    "com.cashop.language"})
@MapperScan({"com.cashop.translate.dao.mapper", "com.cashop.message.dao.mapper", "com.cashop.address.dao.mapper"})
@EnableDiscoveryClient
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
