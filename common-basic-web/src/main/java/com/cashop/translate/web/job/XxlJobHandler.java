package com.cashop.translate.web.job;

import com.cashop.translate.service.job.BatchTranslateJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * XXL-Job任务处理器
 * 
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "translate.image.batch.result.type", havingValue = "xxl-job")
public class XxlJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(XxlJobHandler.class);

    @Autowired
    private BatchTranslateJobService batchTranslateJobService;

    /**
     * 批量翻译结果获取任务
     */
    @XxlJob("batchTranslateResultJob")
    public void batchTranslateResultJob() {
        logger.info("XXL-Job执行批量翻译结果获取任务");
        try {
            batchTranslateJobService.batchTranslateResultJob();
        } catch (Exception e) {
            logger.error("XXL-Job批量翻译结果获取任务执行异常", e);
            throw e;
        }
    }

    /**
     * 清理超时任务
     */
    @XxlJob("cleanTimeoutTasksJob")
    public void cleanTimeoutTasksJob() {
        logger.info("XXL-Job执行清理超时任务");
        try {
            batchTranslateJobService.cleanTimeoutTasksJob();
        } catch (Exception e) {
            logger.error("XXL-Job清理超时任务执行异常", e);
            throw e;
        }
    }
}
