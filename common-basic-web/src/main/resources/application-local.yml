spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************************************
    username: stable78
    password: T1cMe8U183
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: CommonBasicHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.cashop.translate.dao.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

eureka:
  client:
    service-url:
      defaultZone: http://merchant:<EMAIL>:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

apollo:
  cluster: stable
  meta: http://apollo.castable.hk:8080

# XXL-Job配置
xxl:
  job:
    admin:
      addresses: http://xxl-job.castable.hk
    executor:
      appname: common-basic
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

# 翻译服务配置
translate:
  # 阿里云配置
  aliyun:
    access-key-id: LTAI5tKGqw37qyur8fzKuxJP
    access-key-secret: ******************************
    region: cn-hangzhou

  # 易可图配置
  yiketu:
    app-key: **********
    app-secret: db8870ca84e32b7ea0bf10a61d2eaa01
    base-url: https://open-api.yiketu.com

  # 鬼手剪辑配置
  ghostcut:
    app-key: e8d732890c194fee8a167ccb23d46d10
    app-secret: e50b179761c6454ab91add516a124c07
    base-url: https://api.zhaoli.com

  # 图片翻译配置
  image:
    # 批量翻译结果获取类型：local-job 或 xxl-job
    batch:
      result:
        type: local-job

  # 同步单张图片翻译提供商权重配置
  sync-image-single-provider-weight:
    aliyun: 100
  #  aliyun: 50
  #  yiketu: 30
  #  ghostcut: 20

  # 异步批量图片翻译提供商权重配置
  async-image-batch-provider-weight:
    aliyun: 100
#    aliyun: 70
#    ghostcut: 30

  # 同步单条文本翻译提供商权重配置
  sync-text-single-provider-weight:
    aliyun: 100

  # 异步批量文本翻译提供商权重配置
  async-text-batch-provider-weight:
    aliyun: 100
