package com.cashop.message.web.controller;

import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.facade.dto.EmailSendResponse;
import com.cashop.message.service.MessageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 消息控制器测试
 * 
 * <AUTHOR>
 */
@WebMvcTest(MessageController.class)
public class MessageControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MessageService messageService;

    @Autowired
    private ObjectMapper objectMapper;

    private EmailSendRequest emailSendRequest;
    private EmailSendResponse emailSendResponse;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        emailSendRequest = new EmailSendRequest();
        emailSendRequest.setRequestId("test_req_001");
        emailSendRequest.setAppId("test_app");
        emailSendRequest.setSceneCode("user_register");
        emailSendRequest.setLanguage("en");
        emailSendRequest.setToEmailList(Arrays.asList("<EMAIL>"));
        
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("userName", "Test User");
        templateParams.put("verificationCode", "123456");
        templateParams.put("appName", "Test App");
        templateParams.put("expireMinutes", "10");
        emailSendRequest.setTemplateParams(templateParams);

        emailSendResponse = new EmailSendResponse();
        emailSendResponse.setRequestId("test_req_001");
        emailSendResponse.setSuccess(true);
        emailSendResponse.setMessage("邮件发送成功");
        emailSendResponse.setSendStatus("SUCCESS");
        emailSendResponse.setActualTitle("Registration Verification Code");
        emailSendResponse.setProcessingTime(150L);
    }

    @Test
    void testSendEmail_Success() throws Exception {
        // Mock服务层返回成功响应
        when(messageService.sendEmail(any(EmailSendRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(emailSendResponse));

        // 执行请求并验证结果
        mockMvc.perform(post("/api/message/email/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(emailSendRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.requestId").value("test_req_001"))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.sendStatus").value("SUCCESS"));
    }

    @Test
    void testSendEmail_Failure() throws Exception {
        // Mock服务层返回失败响应
        EmailSendResponse failureResponse = new EmailSendResponse();
        failureResponse.setRequestId("test_req_001");
        failureResponse.setSuccess(false);
        failureResponse.setMessage("邮件发送失败");
        failureResponse.setSendStatus("FAILED");
        failureResponse.setErrorMessage("SMTP连接失败");

        when(messageService.sendEmail(any(EmailSendRequest.class)))
                .thenReturn(CompletableFuture.completedFuture(failureResponse));

        // 执行请求并验证结果
        mockMvc.perform(post("/api/message/email/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(emailSendRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("SMTP连接失败"));
    }

    @Test
    void testSendEmail_ValidationError() throws Exception {
        // 测试验证失败的情况
        EmailSendRequest invalidRequest = new EmailSendRequest();
        // 缺少必填字段

        mockMvc.perform(post("/api/message/email/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testRetryEmailSend_Success() throws Exception {
        // Mock服务层返回重试成功响应
        when(messageService.retryEmailSend(anyString()))
                .thenReturn(CompletableFuture.completedFuture(emailSendResponse));

        // 执行请求并验证结果
        mockMvc.perform(post("/api/message/email/retry/test_req_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.requestId").value("test_req_001"))
                .andExpect(jsonPath("$.data.success").value(true));
    }

    @Test
    void testGetEmailSendStatus_Success() throws Exception {
        // Mock服务层返回状态查询响应
        when(messageService.getEmailSendStatus(anyString()))
                .thenReturn(emailSendResponse);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/message/email/status/test_req_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.requestId").value("test_req_001"))
                .andExpect(jsonPath("$.data.sendStatus").value("SUCCESS"));
    }

    @Test
    void testGetEmailSendStatus_NotFound() throws Exception {
        // Mock服务层返回未找到记录的响应
        EmailSendResponse notFoundResponse = new EmailSendResponse();
        notFoundResponse.setRequestId("test_req_001");
        notFoundResponse.setSuccess(false);
        notFoundResponse.setErrorMessage("未找到对应的邮件发送记录");

        when(messageService.getEmailSendStatus(anyString()))
                .thenReturn(notFoundResponse);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/message/email/status/test_req_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("未找到对应的邮件发送记录"));
    }
}
