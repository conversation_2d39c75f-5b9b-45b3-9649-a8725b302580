package com.cashop.message.integration;

import com.cashop.message.dao.entity.MessageEmailRequest;
import com.cashop.message.dao.entity.MessageTemplate;
import com.cashop.message.dao.entity.MessageProvider;
import com.cashop.message.dao.mapper.MessageEmailRequestMapper;
import com.cashop.message.dao.mapper.MessageTemplateMapper;
import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.dao.mapper.MessageProviderMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 消息发送功能集成测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class MessageIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private MessageTemplateMapper messageTemplateMapper;

    @Autowired
    private MessageProviderMapper messageProviderMapper;

    @Autowired
    private MessageEmailRequestMapper messageEmailRequestMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 初始化测试数据
        initTestData();
    }

    private void initTestData() {
        // 创建消息供应商
        MessageProvider provider = new MessageProvider();
        provider.setMessageType("email");
        provider.setProviderKey("test_email");
        provider.setProviderUserName("<EMAIL>");
        provider.setProviderUserPwd("password123");
        provider.setProviderConfig("{\"host\":\"smtp.example.com\",\"port\":587}");
        messageProviderMapper.insert(provider);

        // 创建消息模版
        MessageTemplate template = new MessageTemplate();
        template.setSceneCode("test_register");
        template.setLanguage("en");
        template.setMessageType("email");
        template.setMessageTitle("Test Registration Code");
        template.setMessageContent("Hello ${userName}, your code is ${code}");
        template.setRetryConfig(true);
        template.setProviderKey("test_email");
        messageTemplateMapper.insert(template);
    }

    @Test
    void testEmailSendWorkflow() throws Exception {
        // 1. 准备邮件发送请求
        EmailSendRequest request = new EmailSendRequest();
        request.setAppId("test_app");
        request.setSceneCode("test_register");
        request.setLanguage("en");
        request.setToEmailList(Arrays.asList("<EMAIL>"));
        request.setEmailHost("smtp.example.com");
        request.setEmailPort(587);
        request.setFromEmail("<EMAIL>");
        request.setFromEmailPwd("senderpass");

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("userName", "Test User");
        templateParams.put("code", "123456");
        request.setTemplateParams(templateParams);

        // 2. 发送邮件请求
        String response = mockMvc.perform(post("/api/message/email/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 3. 解析响应获取requestId
        Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);
        Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
        String requestId = (String) data.get("requestId");

        assertNotNull(requestId);

        // 4. 验证数据库记录
        MessageEmailRequest emailRequest = messageEmailRequestMapper.selectByRequestId(requestId);
        assertNotNull(emailRequest);
        assertEquals("test_app", emailRequest.getAppId());
        assertEquals("test_register", emailRequest.getSceneCode());
        assertEquals("en", emailRequest.getLanguage());

        // 5. 查询邮件发送状态
        mockMvc.perform(get("/api/message/email/status/" + requestId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.requestId").value(requestId));
    }

    @Test
    void testEmailSendWithoutTemplate() throws Exception {
        // 测试没有对应模版的情况
        EmailSendRequest request = new EmailSendRequest();
        request.setAppId("test_app");
        request.setSceneCode("nonexistent_scene");
        request.setLanguage("en");
        request.setToEmailList(Arrays.asList("<EMAIL>"));

        mockMvc.perform(post("/api/message/email/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));
    }

    @Test
    void testEmailSendValidation() throws Exception {
        // 测试请求参数验证
        EmailSendRequest request = new EmailSendRequest();
        // 缺少必填字段

        mockMvc.perform(post("/api/message/email/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testTemplateParameterReplacement() {
        // 测试模版参数替换功能
        MessageTemplate template = messageTemplateMapper.selectBySceneAndLanguageAndType(
                "test_register", "en", "email");
        
        assertNotNull(template);
        assertEquals("Test Registration Code", template.getMessageTitle());
        assertTrue(template.getMessageContent().contains("${userName}"));
        assertTrue(template.getMessageContent().contains("${code}"));
    }

    @Test
    void testProviderConfiguration() {
        // 测试供应商配置
        MessageProvider provider = messageProviderMapper.selectByTypeAndKey("email", "test_email");
        
        assertNotNull(provider);
        assertEquals("<EMAIL>", provider.getProviderUserName());
        assertEquals("password123", provider.getProviderUserPwd());
        assertTrue(provider.getProviderConfig().contains("smtp.example.com"));
    }
}
