package com.cashop.translate.web.config;

import com.cashop.translate.service.config.ProviderRouteConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * XXL-Job配置测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class XxlJobConfigTest {

    @Autowired(required = false)
    private XxlJobConfig xxlJobConfig;

    @Autowired
    private ProviderRouteConfig providerRouteConfig;

    @Test
    public void testProviderRouteConfig() {
        assertNotNull(providerRouteConfig);
        
        // 测试配置映射是否正确
        String batchResultType = providerRouteConfig.getBatchResultType();
        assertNotNull(batchResultType);
        
        System.out.println("当前批量翻译结果获取类型: " + batchResultType);
        
        // 在测试环境中，应该使用默认值 local-job
        assertEquals("local-job", batchResultType);
    }

    @Test
    public void testXxlJobConfigConditional() {
        // 在测试环境中，由于配置为 local-job，XxlJobConfig 不应该被加载
        assertNull(xxlJobConfig, "在测试环境中 XxlJobConfig 不应该被加载");
    }
}
