package com.cashop.translate.web.controller;

import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.common.enums.CallbackStatusEnum;
import com.cashop.translate.dao.entity.TranslateRequestRecord;
import com.cashop.translate.dao.mapper.TranslateRequestRecordMapper;
import com.cashop.translate.service.job.TranslateCallbackJobService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.UUID;

/**
 * 回调功能集成测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("local")
public class CallbackIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(CallbackIntegrationTest.class);

    @Autowired
    private TranslateRequestRecordMapper translateRequestRecordMapper;

    @Autowired
    private TranslateCallbackJobService translateCallbackJobService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testCallbackFieldsInDatabase() {
        logger.info("开始测试数据库回调字段");
        
        try {
            // 创建测试记录
            TranslateRequestRecord record = new TranslateRequestRecord();
            record.setRequestId(UUID.randomUUID().toString());
            record.setRequestType("SYNC_SINGLE");
            record.setProvider("YIKETU");
            record.setSourceLanguage("zh");
            record.setTargetLanguage("en");
            record.setImageCount(1);
            record.setRequestParams("{}");
            record.setRequestStatus("SUCCESS");
            record.setCloudApiStatus("SUCCESS");
            record.setRetryCount(0);
            record.setMaxRetryCount(3);
            
            // 设置回调相关字段
            record.setCallback("http://example.com/callback");
            record.setCallbackStatus(CallbackStatusEnum.INITIAL.getCode());
            record.setCallbackRetryCount(0);
            record.setCallbackMaxRetryCount(3);
            
            // 插入记录
            int insertResult = translateRequestRecordMapper.insert(record);
            logger.info("插入记录结果: {}, recordId: {}", insertResult, record.getId());
            
            // 查询记录验证
            TranslateRequestRecord savedRecord = translateRequestRecordMapper.selectById(record.getId());
            logger.info("查询到的记录 - callback: {}, callbackStatus: {}, callbackRetryCount: {}, callbackMaxRetryCount: {}", 
                    savedRecord.getCallback(), 
                    savedRecord.getCallbackStatus(), 
                    savedRecord.getCallbackRetryCount(), 
                    savedRecord.getCallbackMaxRetryCount());
            
            // 验证字段值
            assert "http://example.com/callback".equals(savedRecord.getCallback()) : "回调地址不匹配";
            assert CallbackStatusEnum.INITIAL.getCode().equals(savedRecord.getCallbackStatus()) : "回调状态不匹配";
            assert Integer.valueOf(0).equals(savedRecord.getCallbackRetryCount()) : "回调重试次数不匹配";
            assert Integer.valueOf(3).equals(savedRecord.getCallbackMaxRetryCount()) : "回调最大重试次数不匹配";
            
            logger.info("数据库回调字段测试通过");
            
        } catch (Exception e) {
            logger.error("数据库回调字段测试失败", e);
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    public void testCallbackRecordQuery() {
        logger.info("开始测试回调记录查询");
        
        try {
            // 查询需要回调的记录
            List<TranslateRequestRecord> pendingRecords = translateRequestRecordMapper.selectPendingCallbackRecords(
                    CallbackStatusEnum.INITIAL.getCode(), 3);
            
            logger.info("查询到{}条需要回调的记录", pendingRecords.size());
            
            for (TranslateRequestRecord record : pendingRecords) {
                logger.info("回调记录 - recordId: {}, requestId: {}, callback: {}, status: {}", 
                        record.getId(), 
                        record.getRequestId(), 
                        record.getCallback(), 
                        record.getCallbackStatus());
            }
            
        } catch (Exception e) {
            logger.error("回调记录查询测试失败", e);
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    public void testCallbackJobService() {
        logger.info("开始测试回调任务服务");
        
        try {
            // 检查服务是否正常
            boolean isRunning = translateCallbackJobService.isRunning();
            logger.info("回调任务服务运行状态: {}", isRunning);
            
            // 执行一次回调任务（注意：这会实际发送HTTP请求）
            logger.info("执行回调任务...");
            translateCallbackJobService.executeCallbackJob();
            logger.info("回调任务执行完成");
            
        } catch (Exception e) {
            logger.error("回调任务服务测试失败", e);
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    public void testTranslateRequestWithCallback() {
        logger.info("开始测试带回调的翻译请求");
        
        try {
            // 创建带回调的翻译请求
            TranslateRequest request = new TranslateRequest();
            request.setRequestId(UUID.randomUUID().toString());
            request.setImageUrls(List.of("https://example.com/test.jpg"));
            request.setSourceLanguage("zh");
            request.setTargetLanguage("en");
            request.setCallback("http://example.com/callback");
            
            logger.info("创建的翻译请求 - requestId: {}, callback: {}", 
                    request.getRequestId(), request.getCallback());
            
            // 验证请求对象
            assert request.getCallback() != null : "回调地址不能为空";
            assert "http://example.com/callback".equals(request.getCallback()) : "回调地址不匹配";
            
            logger.info("带回调的翻译请求测试通过");
            
        } catch (Exception e) {
            logger.error("带回调的翻译请求测试失败", e);
            throw new RuntimeException("测试失败", e);
        }
    }

    @Test
    public void testCallbackStatusUpdate() {
        logger.info("开始测试回调状态更新");
        
        try {
            // 创建测试记录
            TranslateRequestRecord record = new TranslateRequestRecord();
            record.setRequestId(UUID.randomUUID().toString());
            record.setRequestType("SYNC_SINGLE");
            record.setProvider("YIKETU");
            record.setSourceLanguage("zh");
            record.setTargetLanguage("en");
            record.setImageCount(1);
            record.setRequestParams("{}");
            record.setRequestStatus("SUCCESS");
            record.setCloudApiStatus("SUCCESS");
            record.setRetryCount(0);
            record.setMaxRetryCount(3);
            record.setCallback("http://example.com/callback");
            record.setCallbackStatus(CallbackStatusEnum.INITIAL.getCode());
            record.setCallbackRetryCount(0);
            record.setCallbackMaxRetryCount(3);
            
            // 插入记录
            translateRequestRecordMapper.insert(record);
            logger.info("插入测试记录，recordId: {}", record.getId());
            
            // 更新回调状态
            int updateResult = translateRequestRecordMapper.updateCallbackStatus(
                    record.getId(), 
                    CallbackStatusEnum.SUCCESS.getCode(), 
                    "回调成功", 
                    1);
            
            logger.info("更新回调状态结果: {}", updateResult);
            
            // 验证更新结果
            TranslateRequestRecord updatedRecord = translateRequestRecordMapper.selectById(record.getId());
            logger.info("更新后的记录 - callbackStatus: {}, callbackResponse: {}, callbackRetryCount: {}", 
                    updatedRecord.getCallbackStatus(), 
                    updatedRecord.getCallbackResponse(), 
                    updatedRecord.getCallbackRetryCount());
            
            // 验证更新是否成功
            assert CallbackStatusEnum.SUCCESS.getCode().equals(updatedRecord.getCallbackStatus()) : "回调状态更新失败";
            assert "回调成功".equals(updatedRecord.getCallbackResponse()) : "回调响应更新失败";
            assert Integer.valueOf(1).equals(updatedRecord.getCallbackRetryCount()) : "回调重试次数更新失败";
            
            logger.info("回调状态更新测试通过");
            
        } catch (Exception e) {
            logger.error("回调状态更新测试失败", e);
            throw new RuntimeException("测试失败", e);
        }
    }
}
