-- H2数据库测试表结构

-- 消息模版表
CREATE TABLE message_template (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  scene_code VARCHAR(64) NOT NULL,
  language VARCHAR(10) NOT NULL DEFAULT 'en',
  message_type VARCHAR(20) NOT NULL,
  message_title VARCHAR(255) NOT NULL,
  message_content TEXT NOT NULL,
  message_remark VARCHAR(500),
  retry_config BOOLEAN DEFAULT TRUE,
  provider_key VARCHAR(64) NOT NULL,
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 消息供应商表
CREATE TABLE message_provider (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  message_type VARCHAR(20) NOT NULL,
  provider_key VARCHAR(64) NOT NULL,
  provider_user_name VARCHAR(255) NOT NULL,
  provider_user_pwd VARCHAR(255) NOT NULL,
  provider_config TEXT,
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 邮件发送记录表
CREATE TABLE message_email_request (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  request_id VARCHAR(64) NOT NULL,
  app_id VARCHAR(64) NOT NULL,
  scene_code VARCHAR(64) NOT NULL,
  language VARCHAR(10) NOT NULL DEFAULT 'en',
  email_host VARCHAR(255),
  email_port INTEGER,
  from_email VARCHAR(255),
  from_email_pwd VARCHAR(255),
  to_email_list TEXT NOT NULL,
  cc_email_list TEXT,
  attachment_list TEXT,
  request_title VARCHAR(500),
  template_params TEXT,
  actual_title VARCHAR(500),
  actual_content TEXT,
  current_retry_count INTEGER DEFAULT 0,
  max_retry_count INTEGER DEFAULT 3,
  send_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  error_message TEXT,
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 翻译请求记录表（用于兼容现有测试）
CREATE TABLE translate_request_record (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  request_id VARCHAR(64) NOT NULL,
  request_type VARCHAR(20) NOT NULL,
  provider VARCHAR(20) NOT NULL,
  source_language VARCHAR(10),
  target_language VARCHAR(10) NOT NULL,
  image_count INTEGER DEFAULT 1,
  request_params TEXT,
  request_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  cloud_api_status VARCHAR(20),
  cloud_api_response TEXT,
  cloud_api_async_status VARCHAR(20),
  cloud_api_async_response TEXT,
  task_id VARCHAR(64),
  retry_count INTEGER DEFAULT 0,
  max_retry_count INTEGER DEFAULT 3,
  error_message TEXT,
  image_sync_result_url VARCHAR(128),
  image_batch_results TEXT,
  translated_text TEXT,
  callback VARCHAR(500),
  callback_status INTEGER DEFAULT 0,
  callback_response TEXT,
  callback_retry_count INTEGER DEFAULT 0,
  callback_max_retry_count INTEGER DEFAULT 3,
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
