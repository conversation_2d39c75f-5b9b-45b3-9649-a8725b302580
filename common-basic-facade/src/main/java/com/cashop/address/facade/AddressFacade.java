package com.cashop.address.facade;

import com.cashop.address.facade.dto.*;
import com.cashop.common.base.response.Result;
import com.cashop.message.facade.MessageFacade;
import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.facade.dto.EmailSendResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "common-basic", path = "/base/common-basic/open/address/api", fallbackFactory = com.cashop.address.facade.AddressFacade.AddressFacadeFallbackFactory.class)
public interface AddressFacade {


    @Operation(summary = "查询所有国家列表", description = "获取所有可用的国家列表")
    @GetMapping("/countries")
    public Result<List<AddressVO>> getAllCountries(@RequestParam(name="language", defaultValue = "en" ) String language);

    @Operation(summary = "根据国家编码查询省级行政区划列表", description = "根据指定的国家编码查询该国家下的所有省/州")
    @GetMapping("/provinces")
    public Result<List<AddressVO>> getProvincesByCountryCode(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode) ;

    @Operation(summary = "根据省编码查询市级行政区划列表", description = "根据指定的省编码查询该省下的所有市/区")
    @GetMapping("/cities")
    public Result<List<AddressVO>> getCitiesByProvinceCode(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "省编码", required = true, example = "3907")
            @RequestParam("provinceCode") String provinceCode) ;

    @Operation(summary = "根据国家编码查询省市级列表", description = "根据国家编码查询省市级列表")
    @GetMapping("/l1l2")
    public Result<List<AddressVO>> getCitiesProvinceByCountryCode(
            @RequestParam(name="language", defaultValue = "en" ) String language,
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode);
    
    @Component
    public class AddressFacadeFallbackFactory implements FallbackFactory<AddressFacade> {
        
        private static final Logger logger = LoggerFactory.getLogger(AddressFacadeFallbackFactory.class);

        @Override
        public AddressFacade create(Throwable cause) {
                return new AddressFacade() {

                    @Override
                    public Result<List<AddressVO>> getAllCountries(String language) {
                        logger.error("调用AddressFacade的getAllCountries方法失败", cause);
                        return Result.error(cause.getMessage());
                    }
                    
                    @Override
                    public Result<List<AddressVO>> getProvincesByCountryCode(String language,String countryCode) {
                        logger.error("调用AddressFacade的getProvincesByCountryCode方法失败", cause);
                        return Result.error(cause.getMessage());
                    }

                    @Override
                    public Result<List<AddressVO>> getCitiesByProvinceCode(String language,String provinceCode) {
                        logger.error("调用AddressFacade的getCitiesByProvinceCode方法失败", cause);
                        return Result.error(cause.getMessage());
                    }

                    @Override
                    public Result<List<AddressVO>> getCitiesProvinceByCountryCode(String language,String countryCode) {
                        logger.error("调用AddressFacade的getCitiesProvinceByCountryCode方法失败", cause);
                        return Result.error(cause.getMessage());
                    }

                };
        }


    }


}
