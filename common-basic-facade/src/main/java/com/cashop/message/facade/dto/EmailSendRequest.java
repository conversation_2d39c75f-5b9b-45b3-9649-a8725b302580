package com.cashop.message.facade.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;
import java.util.Map;


import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 邮件发送请求DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "邮件发送请求")
public class EmailSendRequest {

    @Schema(description = "请求ID", example = "req_123456")
    private String requestId;

    @Schema(description = "应用ID", example = "app_001", required = true)
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "场景编码", example = "user_register", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @Schema(description = "语言", example = "en", defaultValue = "en")
    private String language = "en";

    @Schema(description = "接收人列表", example = "[\"<EMAIL>\"]", required = true)
    @NotEmpty(message = "接收人列表不能为空")
    private List<String> toEmailList;

    @Schema(description = "邮件模版参数", example = "{\"userName\":\"张三\",\"code\":\"123456\"}")
    private Map<String, Object> templateParams;

    @Schema(description = "邮件标题", example = "验证码通知")
    private String emailTitle;

    @Schema(description = "邮件服务端smtp-host", example = "smtp.exmail.qq.com")
    private String emailHost;

    @Schema(description = "邮件服务端smtp-port", example = "465")
    private Integer emailPort;

    @Schema(description = "邮件发件人账号", example = "<EMAIL>")
    private String fromEmail;

    @Schema(description = "邮件发件人密码", example = "password123")
    private String fromEmailPwd;

    @Schema(description = "邮件抄送人列表", example = "[\"<EMAIL>\"]")
    private List<String> ccEmailList;

    @Schema(description = "邮件附件列表")
    private List<EmailAttachment> attachmentList;

    // Getters and Setters
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public List<String> getToEmailList() {
        return toEmailList;
    }

    public void setToEmailList(List<String> toEmailList) {
        this.toEmailList = toEmailList;
    }

    public Map<String, Object> getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(Map<String, Object> templateParams) {
        this.templateParams = templateParams;
    }

    public String getEmailTitle() {
        return emailTitle;
    }

    public void setEmailTitle(String emailTitle) {
        this.emailTitle = emailTitle;
    }

    public String getEmailHost() {
        return emailHost;
    }

    public void setEmailHost(String emailHost) {
        this.emailHost = emailHost;
    }

    public Integer getEmailPort() {
        return emailPort;
    }

    public void setEmailPort(Integer emailPort) {
        this.emailPort = emailPort;
    }

    public String getFromEmail() {
        return fromEmail;
    }

    public void setFromEmail(String fromEmail) {
        this.fromEmail = fromEmail;
    }

    public String getFromEmailPwd() {
        return fromEmailPwd;
    }

    public void setFromEmailPwd(String fromEmailPwd) {
        this.fromEmailPwd = fromEmailPwd;
    }

    public List<String> getCcEmailList() {
        return ccEmailList;
    }

    public void setCcEmailList(List<String> ccEmailList) {
        this.ccEmailList = ccEmailList;
    }

    public List<EmailAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<EmailAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }

    @Override
    public String toString() {
        return "EmailSendRequest{" +
                "requestId='" + requestId + '\'' +
                ", appId='" + appId + '\'' +
                ", sceneCode='" + sceneCode + '\'' +
                ", language='" + language + '\'' +
                ", toEmailList=" + toEmailList +
                ", templateParams=" + templateParams +
                ", emailTitle='" + emailTitle + '\'' +
                ", emailHost='" + emailHost + '\'' +
                ", emailPort=" + emailPort +
                ", fromEmail='" + fromEmail + '\'' +
                ", ccEmailList=" + ccEmailList +
                ", attachmentList=" + attachmentList +
                '}';
    }
}
