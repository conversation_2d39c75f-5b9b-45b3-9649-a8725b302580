package com.cashop.message.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

//import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 邮件发送响应DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "邮件发送响应")
public class EmailSendResponse {

    @Schema(description = "请求ID", example = "req_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "邮件发送成功")
    private String message;

    @Schema(description = "发送状态", example = "SUCCESS")
    private String sendStatus;

    @Schema(description = "实际发送标题", example = "验证码通知")
    private String actualTitle;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "处理时间（毫秒）", example = "150")
    private Long processingTime;

    // Getters and Setters
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getActualTitle() {
        return actualTitle;
    }

    public void setActualTitle(String actualTitle) {
        this.actualTitle = actualTitle;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    @Override
    public String toString() {
        return "EmailSendResponse{" +
                "requestId='" + requestId + '\'' +
                ", success=" + success +
                ", message='" + message + '\'' +
                ", sendStatus='" + sendStatus + '\'' +
                ", actualTitle='" + actualTitle + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", processingTime=" + processingTime +
                '}';
    }
}
