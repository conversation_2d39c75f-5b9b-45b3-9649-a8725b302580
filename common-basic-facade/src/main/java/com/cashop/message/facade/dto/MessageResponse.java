package com.cashop.message.facade.dto;

public class MessageResponse {

    private Boolean success;

    private String message;

    private String responseText;

    public static MessageResponse buildSuccessResponse(String message, String responseText) {
        MessageResponse response = new MessageResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setResponseText(responseText);
        return response;
    }

    public static MessageResponse buildErrorResponse(String message) {
        MessageResponse response = new MessageResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    } 

    public String getResponseText() {
        return responseText;
    }

    public void setResponseText(String responseText) {
        this.responseText = responseText;
    }
}
