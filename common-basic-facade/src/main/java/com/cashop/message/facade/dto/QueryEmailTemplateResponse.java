package com.cashop.message.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "邮件模版查询响应")
public class QueryEmailTemplateResponse {

    /**
     * 场景编码
     */
    @Schema(description = "场景编码", example = "user_register")
    private String sceneCode;

    /**
     * 语言
     */
    @Schema(description = "语言", example = "en")
    private String language;

    /**
     * 模版标题
     */
    @Schema(description = "模版标题", example = "注册验证码")
    private String messageTitle;

    /**
     * 模版内容
     */
    @Schema(description = "模版内容", example = "您的验证码是：${code}")
    private String messageContent;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "用户注册验证码")
    private String messageRemark;

    /**
     * 消息接收人列表
     */
    @Schema(description = "消息接收人列表", example = "<EMAIL>,<EMAIL>")
    private String recipients;

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageRemark() {
        return messageRemark;
    }

    public void setMessageRemark(String messageRemark) {
        this.messageRemark = messageRemark;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    @Override
    public String toString() {
        return "QueryEmailTemplateResponse{" +
                "sceneCode='" + sceneCode + '\'' +
                ", language='" + language + '\'' +
                ", messageTitle='" + messageTitle + '\'' +
                ", messageContent='" + messageContent + '\'' +
                ", messageRemark='" + messageRemark + '\'' +
                ", recipients='" + recipients + '\'' +
                '}';
    }

}
