package com.cashop.message.facade;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cashop.common.base.response.Result;
import com.cashop.message.facade.dto.EmailSendRequest;
import com.cashop.message.facade.dto.EmailSendResponse;
import com.cashop.message.facade.dto.QueryEmailTemplateRequest;
import com.cashop.message.facade.dto.QueryEmailTemplateResponse;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;


@FeignClient(name = "common-basic", path = "/api/message", fallbackFactory = com.cashop.message.facade.MessageFacade.MessageFacadeFallbackFactory.class)
public interface MessageFacade {


    /**
     * 发送邮件
     * 
     * @param request 邮件发送请求
     * @return 邮件发送响应
     */
    @Operation(summary = "发送邮件", description = "根据场景编码和模版参数发送邮件")
    @PostMapping("/email/send")
    public Result<EmailSendResponse> sendEmail(@Valid @RequestBody EmailSendRequest request);

     /**
     * 查询邮件模版
     * 
     * @param request 邮件模版查询请求
     * @return 邮件模版查询响应
     */
    @Operation(summary = "发送邮件", description = "根据场景编码和模版参数发送邮件")
    @PostMapping("/query/email/template")
    public Result<QueryEmailTemplateResponse> queryEmailTemplate(@Valid @RequestBody QueryEmailTemplateRequest request);

    @Component
    public class MessageFacadeFallbackFactory implements FallbackFactory<MessageFacade> {
        
        private static final Logger logger = LoggerFactory.getLogger(MessageFacadeFallbackFactory.class);

        @Override
        public MessageFacade create(Throwable cause) {
                return new MessageFacade() {

                    @Override
                    public Result<EmailSendResponse> sendEmail(@Valid EmailSendRequest request) {
                        logger.error("调用MessageFacade的sendEmail方法失败", cause);
                        return Result.error(cause.getMessage());
                    }

                    @Override
                    public Result<QueryEmailTemplateResponse> queryEmailTemplate(@Valid QueryEmailTemplateRequest request) {
                        logger.error("调用MessageFacade的queryEmailTemplate方法失败", cause);
                        return Result.error(cause.getMessage());
                    }
                    
                };
        }


    }

}
