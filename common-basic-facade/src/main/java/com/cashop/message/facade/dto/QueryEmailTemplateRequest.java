package com.cashop.message.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;


@Schema(description = "邮件模版查询请求")
public class QueryEmailTemplateRequest {

    @Schema(description = "应用ID", example = "app_001", required = true)
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    @Schema(description = "场景编码", example = "user_register", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    @Schema(description = "语言", example = "en", required = true)
    private String language = "en";

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "QueryEmailTemplateRequest{" +
                "appId='" + appId + '\'' +
                ", sceneCode='" + sceneCode + '\'' +
                ", language='" + language + '\'' +
                '}';
    }


}
