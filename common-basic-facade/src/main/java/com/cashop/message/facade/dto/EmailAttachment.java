package com.cashop.message.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

//import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 邮件附件DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "邮件附件")
public class EmailAttachment {

    @Schema(description = "附件名称", example = "document.pdf")
    private String fileName;

    @Schema(description = "附件内容类型", example = "application/pdf")
    private String contentType;

    @Schema(description = "附件内容（Base64编码）")
    private String content;

    @Schema(description = "附件URL（与content二选一）")
    private String url;

    // Getters and Setters
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return "EmailAttachment{" +
                "fileName='" + fileName + '\'' +
                ", contentType='" + contentType + '\'' +
                ", content='" + (content != null ? "[Base64 Content]" : null) + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}
