package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 图片同步翻译响应DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "图片同步翻译响应")
public class TranslateImageSyncResponse {

    @Schema(description = "请求ID", example = "req_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "翻译结果状态", example = "SUCCESS")
    private String status;

    @Schema(description = "云服务API响应")
    private String cloudApiResponse;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "处理时间（毫秒）", example = "150")
    private Long processingTime;

    @Schema(description = "源语言", example = "en")
    private String sourceLanguage;

    @Schema(description = "目标语言", example = "zh")
    private String targetLanguage;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "图片Base64编码")
    private String imageBase64;

    @Schema(description = "图片同步翻译结果URL")
    private String translateImageSyncResultUrl;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCloudApiResponse() {
        return cloudApiResponse;
    }

    public void setCloudApiResponse(String cloudApiResponse) {
        this.cloudApiResponse = cloudApiResponse;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    public String getTranslateImageSyncResultUrl() {
        return translateImageSyncResultUrl;
    }

    public void setTranslateImageSyncResultUrl(String translateImageSyncResultUrl) {
        this.translateImageSyncResultUrl = translateImageSyncResultUrl;
    }
    
}
