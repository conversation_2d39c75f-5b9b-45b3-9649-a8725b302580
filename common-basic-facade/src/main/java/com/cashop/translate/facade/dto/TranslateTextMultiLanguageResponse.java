package com.cashop.translate.facade.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "文本翻译多目标语言响应")
public class TranslateTextMultiLanguageResponse {

    @Schema(description = "请求ID/接收回调、主动查询使用", example = "req_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "翻译结果列表")
    private List<TranslateTextResponse> translateTextResponses;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public List<TranslateTextResponse> getTranslateTextResponses() {
        return translateTextResponses;
    }

    public void setTranslateTextResponses(List<TranslateTextResponse> translateTextResponses) {
        this.translateTextResponses = translateTextResponses;
    }

}
