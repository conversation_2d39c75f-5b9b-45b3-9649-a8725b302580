package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 批量文本翻译请求DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "批量文本翻译请求")
public class TranslateTextBatchRequest {

    @Schema(description = "请求ID（可选，未传时使用UUID）", example = "req_batch_123456")
    private String requestId;

    @Schema(description = "源语言（可选，支持自动检测）", example = "en")
    private String sourceLanguage;

    @NotBlank(message = "目标语言不能为空")
    @Schema(description = "目标语言（必填）", example = "zh", required = true)
    private String targetLanguage;

    @NotEmpty(message = "待翻译文本列表不能为空")
    @Schema(description = "待翻译的文本列表（必填）", example = "[\"Hello, world!\", \"How are you?\"]", required = true)
    private List<String> textList;

    @Schema(description = "文本格式类型", example = "text",
            allowableValues = {"text", "html"},
            defaultValue = "text")
    private String formatType = "text";

    @Schema(description = "翻译场景", example = "general",
            allowableValues = {"general", "title", "communication", "medical", "social"},
            defaultValue = "general")
    private String scene = "general";

    @Schema(description = "回调地址（非必填，不为空时当前翻译请求成功后翻译结果回调该接口）",
            example = "https://example.com/callback")
    private String callback;

    @Schema(description = "扩展参数", example = "{\"priority\": \"high\"}")
    private String ext;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public List<String> getTextList() {
        return textList;
    }

    public void setTextList(List<String> textList) {
        this.textList = textList;
    }

    public String getFormatType() {
        return formatType;
    }

    public void setFormatType(String formatType) {
        this.formatType = formatType;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }
}
