package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

/**
 * 图片批量翻译响应DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "图片批量翻译响应")
public class TranslateImageBatchResponse {

    @Schema(description = "请求ID", example = "req_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "任务ID", example = "task_789")
    private String taskId;

    @Schema(description = "翻译结果状态", example = "SUCCESS")
    private String status;

    @Schema(description = "云服务API响应")
    private String cloudApiResponse;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "处理时间（毫秒）", example = "300")
    private Long processingTime;

    @Schema(description = "源语言", example = "en")
    private String sourceLanguage;

    @Schema(description = "目标语言", example = "zh")
    private String targetLanguage;

    @Schema(description = "图片URL列表")
    private List<String> imageUrls;

    @Schema(description = "图片数量", example = "5")
    private Integer imageCount;

    @Schema(description = "翻译场景", example = "general")
    private String scene;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCloudApiResponse() {
        return cloudApiResponse;
    }

    public void setCloudApiResponse(String cloudApiResponse) {
        this.cloudApiResponse = cloudApiResponse;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }

    public Integer getImageCount() {
        return imageCount;
    }

    public void setImageCount(Integer imageCount) {
        this.imageCount = imageCount;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
}
