package com.cashop.translate.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 批量文本翻译响应DTO
 * 
 * <AUTHOR>
 */
@Schema(description = "批量文本翻译响应")
public class TranslateTextBatchResponse {

    @Schema(description = "请求ID", example = "req_batch_123456")
    private String requestId;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "任务ID（用于查询翻译结果）", example = "task_789012")
    private String taskId;

    @Schema(description = "翻译后的文本列表", example = "[\"你好，世界！\", \"你好吗？\"]")
    private List<String> translatedTextList;

    @Schema(description = "源语言", example = "en")
    private String sourceLanguage;

    @Schema(description = "目标语言", example = "zh")
    private String targetLanguage;

    @Schema(description = "原始文本列表", example = "[\"Hello, world!\", \"How are you?\"]")
    private List<String> originalTextList;

    @Schema(description = "错误信息（失败时）")
    private String errorMessage;

    @Schema(description = "处理时间（毫秒）", example = "1500")
    private Long processingTime;

    @Schema(description = "请求状态", example = "PROCESSING",
            allowableValues = {"PENDING", "PROCESSING", "SUCCESS", "FAILED"})
    private String requestStatus;

    @Schema(description = "文本数量", example = "2")
    private Integer textCount;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<String> getTranslatedTextList() {
        return translatedTextList;
    }

    public void setTranslatedTextList(List<String> translatedTextList) {
        this.translatedTextList = translatedTextList;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public List<String> getOriginalTextList() {
        return originalTextList;
    }

    public void setOriginalTextList(List<String> originalTextList) {
        this.originalTextList = originalTextList;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        this.requestStatus = requestStatus;
    }

    public Integer getTextCount() {
        return textCount;
    }

    public void setTextCount(Integer textCount) {
        this.textCount = textCount;
    }
}
