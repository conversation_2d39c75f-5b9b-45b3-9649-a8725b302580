package com.cashop.language.facade;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import com.cashop.common.base.response.Result;

import io.swagger.v3.oas.annotations.Operation;

@FeignClient(name = "common-basic", path = "/base/common-basic/open/language/api", fallbackFactory = com.cashop.language.facade.LanguageFacade.LanguageFacadeFallbackFactory.class)
public interface LanguageFacade {


    @Operation(summary = "查询语言列表", description = "获取所有可用的语言列表")
    @GetMapping("/languages")
    public Result<List<LanguageVO>> getAllLanguages();

    @Component
    public class LanguageFacadeFallbackFactory implements FallbackFactory<LanguageFacade> {

        private static final Logger logger = LoggerFactory.getLogger(LanguageFacadeFallbackFactory.class);

        @Override
        public LanguageFacade create(Throwable cause) {
            return new LanguageFacade() {
                @Override
                public Result<List<LanguageVO>> getAllLanguages() {
                    logger.error("调用LanguageFacade的getAllLanguages方法失败", cause);
                    return Result.error(cause.getMessage());
                }
            };
        }

    }


}
